<template>
	<div class="system-user-dialog-container">
		<el-dialog :close-on-click-modal="false" :title="dataForm.id ? $t('common.editBtn') : $t('common.addBtn')" draggable v-model="visible">
			<el-form :model="dataForm" :rules="dataRules" label-width="110px" ref="dataFormRef" v-loading="loading">
				<el-row :gutter="20">
					<el-col :span="12" class="mb20">
						<el-form-item label="物资类别" prop="materialCategoryId">
							<el-tree-select
								v-model="dataForm.materialCategoryId"
								:data="categoryTree"
								check-strictly
								:render-after-expand="false"
								clearable
								:props="{ label: 'name', value: 'id' }"
							/>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="物资标识" prop="materialIdentify">
							<el-input placeholder="请输入物资标识" v-model="dataForm.materialIdentify"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="24" class="mb20">
						<el-form-item label="物资名称" prop="materialName">
							<el-input clearable placeholder="请输入物资名称" v-model="dataForm.materialName"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="基本计量单位" prop="basicUnit">
							<el-input clearable placeholder="请输入基本计量单位" v-model="dataForm.basicUnit"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="规格型号" prop="modelVersion">
							<el-input clearable placeholder="请输入规格型号" v-model="dataForm.modelVersion"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="供应商/品牌" prop="supplierBrand">
							<el-input clearable placeholder="请输入供应商/品牌" v-model="dataForm.supplierBrand"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="启用状态" prop="status">
							<el-radio-group v-model="dataForm.status">
								<el-radio :key="index" :label="item.value" border v-for="(item, index) in enable_status">{{ item.label }} </el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
					<el-col :span="24" class="mb20">
						<el-form-item label="附件" prop="fileUrlList">
							<upload-file
								:limit="5"
								v-model="dataForm.fileUrlArr"
								@change="handleFileChange"
								:fileType="['png', 'jpg', 'jpeg', 'doc', 'xls', 'ppt', 'txt', 'pdf', 'docx', 'xlsx', 'pptx','zip','rar','zip']"
							/>
						</el-form-item>
					</el-col>
					<el-col :span="24" class="mb20">
						<el-form-item label="备注" prop="remark">
							<el-input maxlength="100" :rows="3" placeholder="请输入" type="textarea" v-model="dataForm.remark"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="visible = false">{{ $t('common.cancelButtonText') }}</el-button>
					<el-button v-debounce="onSubmit" type="primary" :disabled="loading">{{ $t('common.confirmButtonText') }}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" name="systemUserDialog" setup>
import { putObj, getObj, addObj } from '/@/api/basicData/materialManagement';
import { useDict } from '/@/hooks/dict';
import { useI18n } from 'vue-i18n';
import { useMessage } from '/@/hooks/message';
import { rule } from '/@/utils/validate';
import { data } from 'autoprefixer';

const { t } = useI18n();

// 定义刷新表格emit
const emit = defineEmits(['refresh']);
//props
const props = defineProps<{
	categoryTree: Array<any>;
}>();

// @ts-ignore
const { enable_status } = useDict('enable_status');

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
const dataForm = reactive<any>({
	id: '',
	materialCode: '',
	materialCategoryId: '',
	materialName: '',
	materialIdentify: '',
	basicUnit: '',
	modelVersion: '',
	supplierBrand: '',
	remark: '',
	status: '1',
	fileUrl: '',
	fileUrlList: [],
	fileUrlArr: [],
});

const dataRules = ref({
	materialCategoryId: [{ required: true, message: '物资类别不能为空', trigger: 'blur' }],
	materialName: [
		{ validator: rule.overLength, trigger: 'blur' },
		{ required: true, message: '物资名称不能为空', trigger: 'blur' },
	],
	basicUnit: [
		{ validator: rule.overLength, trigger: 'blur' },
		{ required: true, message: '基本计量单位不能为空', trigger: 'blur' },
	],
	modelVersion: [
		{ validator: rule.overLength, trigger: 'blur' },
		{ required: true, message: '规格型号不能为空', trigger: 'blur' },
	],
});

const handleFileChange = (fileNames: string, fileList: any[]) => {
	dataForm.fileUrl = fileList.map((file) => file.fileUrl).join(',');
	dataForm.fileUrlArr = fileList;
};
// 打开弹窗
const openDialog = async (id: string) => {
	visible.value = true;
	dataForm.id = '';
	dataForm.fileUrlArr = [];
	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields();
	});
	// 修改获取用户信息
	if (id) {
		dataForm.id = id;
		getObj(id)
			.then((res) => {
				Object.assign(dataForm, { ...res.data, status: res.data.status.toString() });
				if (dataForm.fileUrlList) {
					dataForm.fileUrlArr = dataForm.fileUrlList.map((item: any) => {
						return {
							name: item.original,
							url: 'admin/sys-file/oss/file?fileName=' + item.fileName,
							fileUrl: item.fileName,
						};
					});
				}
			})
			.catch((err) => {
				useMessage().error(err.msg);
			});
	}
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;
	try {
		const { id, ...addData } = dataForm;
		loading.value = true;
		dataForm.id ? await putObj(dataForm) : await addObj(addData);
		useMessage().success(t(dataForm.id ? 'common.editSuccessText' : 'common.addSuccessText'));
		visible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
