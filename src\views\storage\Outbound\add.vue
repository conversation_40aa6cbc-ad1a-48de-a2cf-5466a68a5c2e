<template>
	<div class="layout-padding">
		<splitpanes>
			<pane size="42">
				<div class="layout-padding-auto layout-padding-view">
					<el-tabs v-model="activeName" @tab-click="handleClick">
						<el-tab-pane label="单物资" lazy name="first">
							<template #label>
								<div class="ml-0">单物资</div>
							</template>
							<material-single @add-to-right="addToRightList" />
						</el-tab-pane>
						<el-tab-pane label="物资组合" lazy name="second">
							<template #label>
								<div class="ml-0">物资组合</div>
							</template>
							<material-combination @add-to-right="addToRightList" />
						</el-tab-pane>
						<el-tab-pane label="场景预案" lazy name="third">
							<template #label>
								<div class="ml-0">场景预案</div>
							</template>
							<generic-plan @add-to-right="addToRightList" />
						</el-tab-pane>
					</el-tabs>
				</div>
			</pane>
			<!-- 右侧 -->
			<pane class="ml8">
				<div class="layout-padding-auto layout-padding-view">
					<el-row class="mt7">
						<div class="text-xl font-bold">新增出库</div>
					</el-row>
					<el-row class="mt20 mb10">
						<el-form ref="queryRef" :inline="true" label-width="100px" :mode="form" :rules="formRules">
							<el-form-item label="出库单编号" v-if="form.id">
								<el-input v-model="form.billCode" clearable style="max-width: 180px" disabled />
							</el-form-item>
							<el-form-item label="出库单状态" v-if="form.id">
								<el-select clearable v-model="form.billStatus" style="width: 180px" disabled>
									<el-option
										:key="item.value"
										:label="item.label"
										:value="item.value"
										v-for="item in [
											{ label: '已出库', value: '2' },
											{ label: '待出库', value: '1' },
											{ label: '待确认', value: '0' },
										]"
									/>
								</el-select>
							</el-form-item>
							<el-form-item label="出库仓库" prop="warehouseId">
								<el-select placeholder="请选择出库仓库" clearable v-model="form.warehouseId" class="!w-[180px]">
									<el-option :key="item.id" :label="item.warehouseName" :value="item.id" v-for="item in warehouseList" />
								</el-select>
							</el-form-item>
							<el-form-item label="出库用途" prop="outPurpose">
								<el-input v-model="form.outPurpose" placeholder="请输入出库用途" clearable style="max-width: 180px" />
							</el-form-item>
							<el-form-item label="是否归还" prop="needReturn">
								<el-select placeholder="请选择是否归还" clearable v-model="form.needReturn" style="width: 180px">
									<el-option
										:key="item.value"
										:label="item.label"
										:value="item.value"
										v-for="item in [
											{ label: '是', value: '1' },
											{ label: '否', value: '0' },
										]"
									/>
								</el-select>
							</el-form-item>
							<el-form-item label="预计归还时间" prop="planReturnTime" v-if="form.needReturn == '1'">
								<el-date-picker
									class="!w-[180px]"
									type="datetime"
									placeholder="请选择预计归还时间"
									v-model="form.planReturnTime"
									value-format="YYYY-MM-DD HH:mm:ss"
								/>
							</el-form-item>
							<el-form-item label="申请部门" prop="applyDeptId">
								<el-select placeholder="请选择申请部门" clearable v-model="form.applyDeptId" style="width: 180px">
									<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in warnType" />
								</el-select>
							</el-form-item>
							<el-form-item label="申请人员" prop="applyUserId">
								<el-select placeholder="请选择申请人员" clearable v-model="form.applyUserId" style="width: 180px">
									<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in warnType" />
								</el-select>
							</el-form-item>
						</el-form>
					</el-row>
					<el-table
						height="calc(100vh - 325px)"
						:data="rightDataList"
						border
						empty-text="暂无数据，请从左侧添加入库信息"
						:cell-style="tableStyle.cellStyle"
						:header-cell-style="tableStyle.headerCellStyle"
					>
						<el-table-column label="序号" type="index" width="60" fixed="left" />
						<el-table-column label="物资编码" prop="materialCode" fixed="left" show-overflow-tooltip></el-table-column>
						<el-table-column label="物资标识" prop="materialIdentify" show-overflow-tooltip></el-table-column>
						<el-table-column label="物资名称" prop="materialName" show-overflow-tooltip width="200"></el-table-column>
						<el-table-column label="数量" prop="materialNum" show-overflow-tooltip width="60"></el-table-column>
						<el-table-column label="单位" prop="basicUnit" show-overflow-tooltip width="60"></el-table-column>
						<el-table-column :label="$t('common.action')" fixed="right" width="60">
							<template #default="scope">
								<el-button v-auth="'sys_user_edit'" text type="primary" @click="deleteClick(scope.row)"> 删除 </el-button>
							</template>
						</el-table-column>
					</el-table>
					<el-row>
						<div class="w-full mt-[15px]">
							<div class="float-left">物资清单（当前总数量 {{ rightDataList.reduce((sum: any, item: any) => sum + item.materialNum, 0) }} ）</div>
							<div class="float-right">
								<el-button @click="returnClick">{{ $t('common.cancelButtonText') }}</el-button>
								<el-button @click="confirmClick" type="primary" :disabled="loading">{{ $t('common.confirmButtonText') }}</el-button>
							</div>
						</div>
					</el-row>
				</div>
			</pane>
		</splitpanes>
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { putObj, getObj } from '/@/api/admin/user';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { TabsPaneContext } from 'element-plus';
// 动态引入组件
const MaterialSingle = defineAsyncComponent(() => import('./tags/materialSingle.vue'));
const MaterialCombination = defineAsyncComponent(() => import('./tags/materialCombination.vue'));
const GenericPlan = defineAsyncComponent(() => import('./tags/genericPlan.vue'));
const router = useRouter();
const route = useRoute();
//tabs切换
const activeName = ref('first');

const handleClick = (tab: TabsPaneContext, event: Event) => {
	console.log(tab, event);
};
const { t } = useI18n();

// 定义变量内容
const queryRef = ref();

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({});
const { tableStyle } = useTable(state);
//确认btn
const loading = ref(false);

//右侧列表数据
let rightDataList = ref<any>([]);
//接收单物资子组件传递的数据

const addToRightList = (row: any, arr?: any[]) => {
  const itemsToAdd = arr || [row];
  const materialCodeMap = new Map(rightDataList.value.map((item:any) => [item.materialCode, item]));

  itemsToAdd.forEach((item: any) => {
    const existingItem:any = materialCodeMap.get(item.materialCode);
    if (existingItem) {
      existingItem.materialNum += item.materialNum;
    } else {
      rightDataList.value.push({ ...item, date: '' });
    }
  });
};

//右侧删除
const deleteClick = (row: any) => {
	rightDataList.value = rightDataList.value.filter((item: any) => item.materialCode != row.materialCode);
};
//新增组合名称

let form = reactive({
	id: '',
	warehouseId: '',
	outPurpose: '',
	needReturn: '',
	planReturnTime: '',
	applyDeptId: '',
	applyUserId: '',
	billCode: '',
	billStatus: '',
});
const formRules = ref({
	warehouseId: [{ required: true, message: '请选择出库库房', trigger: 'blur' }],
});
//右侧提交
const confirmClick = async () => {
	const valid = await queryRef.value.validate().catch(() => {});
	if (!valid) return false;
	try {
		loading.value = true;
		let data = {
			...form,
			billDetailList: rightDataList.value.map((item: any) => ({
				materialCatalogId: item.id || item.materialCatalogId,
				materialCode: item.materialCode,
				num: item.materialNum,
				endEffectiveTime: item.date,
			})),
		};
		route.query?.id ? await putObj({ ...data, id: route.query.id }) : await addObj(data);
		useMessage().success(t(route.query?.id ? 'common.editSuccessText' : 'common.addSuccessText'));
		returnClick();
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};

//获取详情
const getDetails = async () => {
	const { data } = await getObj(route.query?.id as string);
	rightDataList.value = data?.planDetailListVO || [];
	Object.assign(form, { scenePlanDesc: data?.scenePlanDesc, scenePlanName: data?.scenePlanName, status: String(data?.status) });
};

let warehouseList = ref<any>([]);
const getHouseData = async () => {
	let { data } = await getWarehouse();
	warehouseList.value = data;
};
onMounted(async () => {
	await getHouseData();
	if (route.query?.id) {
		await getDetails();
	}
});

const returnClick = () => {
	router.replace({
		path: '/storage/Outbound/index',
	});
};
</script>
