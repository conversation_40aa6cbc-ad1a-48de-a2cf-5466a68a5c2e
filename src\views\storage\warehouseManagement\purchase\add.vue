<template>
	<div class="layout-padding">
		<splitpanes>
			<pane size="42">
				<div class="layout-padding-auto layout-padding-view">
					<el-tabs v-model="activeName" @tab-click="handleClick">
						<el-tab-pane label="单物资" lazy name="first">
							<template #label>
								<div class="ml-0">单物资</div>
							</template>
							<material-single @add-to-right="addToRightList" />
						</el-tab-pane>
						<el-tab-pane label="物资组合" lazy name="second">
							<template #label>
								<div class="ml-0">物资组合</div>
							</template>
							<material-combination @add-to-right="addToRightList" />
						</el-tab-pane>
						<el-tab-pane label="场景预案" lazy name="third">
							<template #label>
								<div class="ml-0">场景预案</div>
							</template>
							<generic-plan @add-to-right="addToRightList" />
						</el-tab-pane>
					</el-tabs>
				</div>
			</pane>
			<!-- 右侧 -->
			<pane class="ml8">
				<div class="layout-padding-auto layout-padding-view">
					<el-row class="mt7">
						<div class="text-xl font-bold">新增入库</div>
					</el-row>
					<el-row class="mt20 mb16">
						<el-form ref="queryRef" :inline="false" label-width="100px" :model="form" :rules="formRules" >
							<el-form-item label="入库仓库" prop="warehouseId">
								<el-select placeholder="请选择入库仓库" clearable v-model="form.warehouseId" class="!w-[180px]">
									<el-option :key="item.id" :label="item.warehouseName" :value="item.id" v-for="item in warehouseList" />
								</el-select>
							</el-form-item>
						</el-form>
					</el-row>
					<el-table
						height="calc(100vh - 270px)"
						:data="rightDataList"
						border
						empty-text="暂无数据，请从左侧添加入库信息"
						:cell-style="tableStyle.cellStyle"
						:header-cell-style="tableStyle.headerCellStyle"
					>
						<el-table-column label="序号" type="index" width="60" fixed="left" />
						<el-table-column label="物资编码" prop="materialCode" fixed="left" show-overflow-tooltip></el-table-column>
						<el-table-column label="物资标识" prop="materialIdentify" show-overflow-tooltip></el-table-column>
						<el-table-column label="物资名称" prop="materialName" show-overflow-tooltip width="200"></el-table-column>
						<el-table-column label="数量" prop="num" show-overflow-tooltip width="60"></el-table-column>
						<el-table-column label="单位" prop="basicUnit" show-overflow-tooltip width="60"></el-table-column>
						<el-table-column label="有效期" show-overflow-tooltip width="150">
							<template #default="scope">
								<el-date-picker v-model="scope.row.endEffectiveTime" type="endEffectiveTime" placeholder="" value-format="YYYY-MM-DD" />
							</template>
						</el-table-column>
						<el-table-column :label="$t('common.action')" fixed="right" width="60">
							<template #default="scope">
								<el-button v-auth="'sys_user_edit'" text type="primary" @click="deleteClick(scope.row)"> 删除 </el-button>
							</template>
						</el-table-column>
					</el-table>
					<el-row class="relative top-[20px]">
						<div style="width: 100%">
							<div class="float-left">物资清单（当前总数量 {{ rightDataList.reduce((sum: any, item: any) => sum + item.num, 0) }} ）</div>
							<div class="float-right">
								<el-button @click="returnClick">{{ $t('common.cancelButtonText') }}</el-button>
								<el-button @click="confirmClick" type="primary" :disabled="loading">{{ $t('common.confirmButtonText') }}</el-button>
							</div>
						</div>
					</el-row>
				</div>
			</pane>
		</splitpanes>
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import {  putObj,addObj,getObj ,getWarehouse} from '/@/api/storage/warehouseManagement/purchase';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { TabsPaneContext } from 'element-plus';
// 动态引入组件
const MaterialSingle = defineAsyncComponent(() => import('./tags/materialSingle.vue'));
const MaterialCombination = defineAsyncComponent(() => import('./tags/materialCombination.vue'));
const GenericPlan = defineAsyncComponent(() => import('./tags/genericPlan.vue'));
const router = useRouter();
const route = useRoute();
//tabs切换
const activeName = ref('first');

const handleClick = (tab: TabsPaneContext, event: Event) => {
	console.log(tab, event);
};
const { t } = useI18n();

// 定义变量内容
const queryRef = ref();
// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({});
const { tableStyle } = useTable(state);
//确认btn
const loading = ref(false);
//右侧列表数据
let rightDataList = ref<any>([]);
//接收单物资子组件传递的数据
const addToRightList = (row: any, arr?: any[]) => {
  const itemsToAdd = arr || [row];
  const materialCodeMap = new Map(rightDataList.value.map((item:any) => [item.materialCode, item]));

  itemsToAdd.forEach((item: any) => {
    const existingItem:any = materialCodeMap.get(item.materialCode);
    if (existingItem) {
      existingItem.num += item.materialNum;
    } else {
      rightDataList.value.push({ ...item, endEffectiveTime: '',num:item.materialNum });
    }
  });
};
//右侧删除
const deleteClick = (row: any) => {
	rightDataList.value = rightDataList.value.filter((item: any) => item.materialCode != row.materialCode);
};


let form = reactive({
	warehouseId: '',
})
const formRules = ref({
	warehouseId: [{ required: true, message: '请选择入库库房', trigger: 'blur' }],
});
//右侧提交
const confirmClick = async () => {

	const valid = await queryRef.value.validate().catch(() => {});
	if (!valid) return false;
	try {
		loading.value = true;
		let data = {
			...form,
			billDetailList: rightDataList.value.map((item: any) => ({
				materialCatalogId: item.id || item.materialCatalogId,
				materialCode: item.materialCode,
				num: item.num,
				endEffectiveTime: item.endEffectiveTime,
			})),
		};
		route.query?.id ? await putObj({ ...data, id: route.query.id }) : await addObj(data);
		useMessage().success(t(route.query?.id ? 'common.editSuccessText' : 'common.addSuccessText'));
		returnClick();
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};

//获取详情
const getDetails = async () => {
	const { data } = await getObj(route.query?.id as string);
	rightDataList.value = data?.billDetailList || [];
	form.warehouseId = data?.warehouseId;
};
//返回上级菜单
const returnClick = () => {
	router.replace({
		path: '/storage/warehouseManagement/purchase/index',
	});
};
let warehouseList=ref<any>([]);
const getHouseData = async () => {
	 let {data} =await getWarehouse();
	 warehouseList.value=data;
}
onMounted(async () => {
	await getHouseData();
	if (route.query?.id) {
		await getDetails();
	}
})
</script>
