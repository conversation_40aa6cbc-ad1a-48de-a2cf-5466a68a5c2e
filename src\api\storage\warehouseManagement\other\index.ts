import request from '/@/utils/request';
//仓库下拉
export const getWarehouse = () => {
	return request({
		url: '/admin/warehouse/selectWarehouseList',
		method: 'get',
	});
};

export const pageList = (obj?: Object) => {
	return request({
		url: '/admin/entryWarehouseBill/other/getEntryWarehouseBillPage',
		method: 'post',
		data: obj,
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
};
//新增
export const addObj = (data?: Object) => {
	return request({
		url: '/admin/entryWarehouseBill/other/addEntryWarehouseBill',
		method: 'post',
		headers: {
			'Content-Type': 'application/json',
		},
		data,
	});
};
export const putObj = (data?: Object) => {
	return request({
		url: '/admin/entryWarehouseBill/purchase/updateEntryWarehouseBill',
		method: 'post',
		data,
	});
};
//删除
export const delObj = (id?: string) => {
	return request({
		url: '/admin/entryWarehouseBill/purchase/deleteEntryWarehouseBillById/' + id,
		method: 'get',
	});
};
//核验
export const checkObj = (id?: any) => {
	return request({
		url: '/admin/entryWarehouseBill/purchase/verifyEntryWarehouseBillById/' + id,
		method: 'get',
	});
};
//查看  详情
export const getObj = (id?: any) => {
	return request({
		url: '/admin/entryWarehouseBill/getEntryWarehouseBillById/' + id,
		method: 'get',
	});
};

export const getMxObj = (id?: any) => {
	return request({
		url: '/admin/entryWarehouseBillDetail/getEntryWarehouseBillDetailById/' + id,
		method: 'get',
	});
};

//pdf 设置返回格式
export const downPdf = (id?: any) => {
	return request({
		url: '/admin/entryWarehouseBill/pdfEntryWarehouseBillById/' + id,
		method: 'get',
		responseType: 'blob',
	});
};

//匹配标签
export const filterTags = (Obj?: object) => {
	return request({
		url: '/admin/material/matchMaterialWarehouse',
		method: 'post',
		data:Obj,
	});
};

//物资编码获取物资目录
export const getMaterialName = (materialCode?: any) => {
	return request({
		url: '/admin/materialCatalog/getMaterialCatalogByMaterialCode/' + materialCode,
		method: 'get',
	});
};
