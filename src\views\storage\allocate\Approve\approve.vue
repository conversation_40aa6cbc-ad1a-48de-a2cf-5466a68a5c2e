<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row class="mt20 mb-2">
				<el-col :span="24">
					<Descriptions title="" :column="4" :label-style="{ fontSize: '14px' }">
						<template #extra> </template>
						<DescriptionsItem label="调拨申请单编号">{{}}</DescriptionsItem>
						<DescriptionsItem label="调拨状态">{{}}</DescriptionsItem>

						<DescriptionsItem label="调出仓库">{{}}</DescriptionsItem>
						<DescriptionsItem label="接收仓库">{{}}</DescriptionsItem>
						<DescriptionsItem label="调拨申请部门">{{}}</DescriptionsItem>
						<DescriptionsItem label="调拨申请人">{{}}</DescriptionsItem>
						<DescriptionsItem label="申请时间">{{}}</DescriptionsItem>
						<DescriptionsItem label="调拨申请原因">{{}}</DescriptionsItem>
					</Descriptions>
				</el-col>
			</el-row>

			<el-table
				v-loading="state.loading"
				:data="tableData"
				height="calc(100vh - 310px)"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
				ref="tableRefs"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="物资编码" prop="username" fixed="left" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资标识" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资名称" prop="name" show-overflow-tooltip width="600"></el-table-column>
				<el-table-column label="数量" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="单位" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="我的库存" prop="name" show-overflow-tooltip></el-table-column>
			</el-table>
			<el-row class="fixed bottom-[15px] right-[20px]">
				<el-button @click="returnClick">取消</el-button>
				<el-button type="primary">确认</el-button>
			</el-row>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { delObj, pageList, putObj } from '/@/api/admin/user';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';

// 动态引入组件

const { t } = useI18n();

// 多选rows
// 是否可以多选

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		deptId: '',
		username: '',
		phone: '',
	},
	pageList: pageList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

const router = useRouter();
const returnClick = () => {
	router.replace({ path: '/storage/allocate/Approve/index' });
};

const tableData = ref<any>([]);
tableData.value = [
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
];
</script>

<style lang="scss" scoped>
pre code.hljs {
	width: 65%;
}
</style>
