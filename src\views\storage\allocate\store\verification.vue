<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row class="">
				<el-col :span="24">
					<Descriptions title="核验入库" :column="5" :label-style="{ fontSize: '14px' }" :content-style="{ fontSize: '16px' }">
						<template #extra> </template>
						<DescriptionsItem label="入库单编号">{{}}</DescriptionsItem>
						<DescriptionsItem label="入库单状态">{{}}</DescriptionsItem>
						<DescriptionsItem label="仓库">{{}}</DescriptionsItem>

						<DescriptionsItem label="创建人员">{{}}</DescriptionsItem>
						<DescriptionsItem label="创建时间">{{}}</DescriptionsItem>
					</Descriptions>
				</el-col>
			</el-row>
			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				height="100%"
				row-key="userId"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="物资编码" prop="username" fixed="left" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资标识" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资名称" prop="name" show-overflow-tooltip width="600"></el-table-column>
				<el-table-column label="数量" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="单位" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="操作" width="200" fixed="right">
					<template #default="scope">
						<!-- 修改信息 -->
						<el-button v-auth="'sys_user_edit'" text type="primary" @click="LogDetailRef.openDialog(scope.row)"> 明细 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-row class="mt20 ">
				<div class="w-full  flex justify-between items-center">
					<div>物资明细（当前条码数量：{{ state.dataList?.length }})</div>
					<div >
						<el-button @click="returnClick">取消</el-button>
						<el-button type="primary">核验无误，入库</el-button>
					</div>
				</div>
			</el-row>
		</div>
		<log-detail ref="LogDetailRef"></log-detail>

	</div>
</template>

<script lang="ts" setup>
import { delObj, pageList, putObj } from '/@/api/admin/user';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
const LogDetail = defineAsyncComponent(() => import('./detail.vue'));

const LogDetailRef = ref();
// 动态引入组件

const { t } = useI18n();

// 多选rows
// 是否可以多选

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		deptId: '',
		username: '',
		phone: '',
	},
	pageList: pageList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

const router = useRouter();
const returnClick = () => {
	router.replace({ path: '/storage/allocate/store/index' });
};
</script>

<style lang="scss" scoped>
pre code.hljs {
	width: 65%;
}
</style>
