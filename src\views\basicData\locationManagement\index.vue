<!-- 货位管理 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-tabs v-model="activeName" @tab-click="handleClick">
				<el-tab-pane lazy label="绑定电子标签" name="label">
					<template #label>
						<div class="ml-8">绑定电子标签</div>
					</template>
					<label-config />
				</el-tab-pane>

				<el-tab-pane lazy label="绑定巷道灯" name="lamp">
					<template #label> 绑定巷道灯 </template>
					<lamp-config />
				</el-tab-pane>
			</el-tabs>
		</div>
	</div>
</template>

<script setup lang="ts" name="systemSysMessage">
// 引入组件
import { TabsPaneContext } from 'element-plus';

const LabelConfig = defineAsyncComponent(() => import('./label/index.vue'));
const LampConfig = defineAsyncComponent(() => import('./lamp/index.vue'));

const activeName = ref('label');

const handleClick = (tab: TabsPaneContext, event: Event) => {
	console.log(tab, event);
};
</script>
