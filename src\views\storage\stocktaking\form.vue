<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="w-full flex justify-between items-center">
					<div class="flex items-center cursor-pointer" @click="()=>router.replace({ path: '/storage/stocktaking/index' })">
						<img src="/@/assets/back.png" class="w-5 h-5 mr-1" />
						<div class="text-14" style="color: #2e5cf6">返回</div>
					</div>

				</div>
			</el-row>
			<el-row class="mt20">
				<el-col :span="24">
					<Descriptions title="" :column="6" :label-style="{ fontSize: '14px' }">
						<template #extra> </template>
						<DescriptionsItem label="盘点任务名称">{{}}</DescriptionsItem>
						<DescriptionsItem label="盘点类型">{{}}</DescriptionsItem>
						<DescriptionsItem label="状态">{{}}</DescriptionsItem>
						<DescriptionsItem label="盘点人员">{{}}</DescriptionsItem>
						<DescriptionsItem label="盘点时间">{{}}</DescriptionsItem>
						<DescriptionsItem label="盘点区域">{{}}</DescriptionsItem>
					</Descriptions>
				</el-col>
			</el-row>

			<el-table
				v-loading="state.loading"
				:data="dataList"
				row-key="userId"
				max-height="calc(100vh - 400px)"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="物资编码" prop="username" fixed="left" show-overflow-tooltip></el-table-column>

				<el-table-column label="物资标识" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资名称" prop="name" show-overflow-tooltip width="600"></el-table-column>
				<el-table-column label="盘点结果" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="操作" width="200" fixed="right">
					<template #default="scope">
						<!-- 修改信息 -->
						<el-button v-auth="'sys_user_edit'" text type="primary" @click="LogDetailRef.openDialog(scope.row)"> 明细 </el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>
		<log-detail ref="LogDetailRef"></log-detail>
	</div>
</template>

<script lang="ts" setup>
import { delObj, pageList, putObj } from '/@/api/admin/user';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
const LogDetail = defineAsyncComponent(() => import('./detail.vue'));

const LogDetailRef = ref();
// 动态引入组件

const { t } = useI18n();

// 多选rows
// 是否可以多选

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		deptId: '',
		username: '',
		phone: '',
	},
	pageList: pageList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

const router = useRouter();
const returnClick = () => {
	router.replace({ path: '/storage/Outbound/index' });
};

const dataList = [
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
];
</script>

<style lang="scss" scoped>
pre code.hljs {
	width: 65%;
}
</style>
