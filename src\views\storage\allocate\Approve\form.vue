<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="w-full flex justify-between items-center">
					<div class="flex items-center cursor-pointer" @click="returnClick">
						<img src="/@/assets/back.png" class="w-5 h-5 mr-1" />
						<div class="text-14" style="color: #2e5cf6">返回</div>
					</div>

				</div>
			</el-row>
			<el-row class="mt20">
				<el-col :span="24">
					<Descriptions title="" :column="4" :label-style="{ fontSize: '14px' }">
						<template #extra> </template>
						<DescriptionsItem label="调拨申请单编号">{{}}</DescriptionsItem>
						<DescriptionsItem label="调拨状态">{{}}</DescriptionsItem>
						<DescriptionsItem label="调出仓库">{{}}</DescriptionsItem>
						<DescriptionsItem label="接收仓库">{{}}</DescriptionsItem>
						<DescriptionsItem label="调拨申请部门">{{}}</DescriptionsItem>
						<DescriptionsItem label="调拨申请人">{{}}</DescriptionsItem>
						<DescriptionsItem label="申请时间">{{}}</DescriptionsItem>
						<DescriptionsItem label="调拨申请原因">{{}}</DescriptionsItem>
						<DescriptionsItem label="审批部门">{{}}</DescriptionsItem>
						<DescriptionsItem label="审批人">{{}}</DescriptionsItem>
						<DescriptionsItem label="审批时间">{{}}</DescriptionsItem>
						<DescriptionsItem label="审批结果">{{}}</DescriptionsItem>
						<DescriptionsItem label="审批意见">{{}}</DescriptionsItem>

					</Descriptions>
				</el-col>
			</el-row>

			<el-table
				v-loading="state.loading"
				:data="dataList"
				row-key="userId"
				max-height="calc(100vh - 400px)"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="物资编码" prop="username" fixed="left" show-overflow-tooltip></el-table-column>

				<el-table-column label="物资标识" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资名称" prop="name" show-overflow-tooltip width="600"></el-table-column>
				<el-table-column label="数量" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="单位" prop="name" show-overflow-tooltip></el-table-column>

			</el-table>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { delObj, pageList, putObj } from '/@/api/admin/user';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';

const LogDetailRef = ref();
// 动态引入组件

const { t } = useI18n();

// 多选rows
// 是否可以多选

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		deptId: '',
		username: '',
		phone: '',
	},
	pageList: pageList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

const router = useRouter();
const returnClick = () => {
	router.replace({ path: '/storage/allocate/Approve/index' });
};

const dataList = [
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
	{ name: '2we' },
];
</script>

<style lang="scss" scoped>
pre code.hljs {
	width: 65%;
}
</style>
