<template>
	<div class="system-user-dialog-container">
		<el-dialog :close-on-click-modal="false" draggable v-model="visible" height="600" width="1200">
			<template #header>
				<el-row>
					<div>
						<span> 物资组合名称： </span>
						<span> {{ name }}</span>
					</div>
				</el-row>
			</template>
			<el-row class="mb4">
				<span> 物资清单 </span>
			</el-row>
			<el-table :data="tableData" border :cell-style="tableStyle.cellStyle" :header-cell-style="tableStyle.headerCellStyle">
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="物资编码" prop="materialCode" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资标识" prop="materialIdentify" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资名称" prop="materialName" show-overflow-tooltip></el-table-column>
				<el-table-column label="数量" prop="materialNum" show-overflow-tooltip></el-table-column>
				<el-table-column label="单位" prop="basicUnit" show-overflow-tooltip></el-table-column>
			</el-table>
		</el-dialog>
	</div>
</template>

<script lang="ts" name="systemUserDialog" setup>
import { useI18n } from 'vue-i18n';
import { BasicTableProps, useTable } from '/@/hooks/table';

const state: BasicTableProps = reactive<BasicTableProps>({});
const { tableStyle } = useTable(state);

const { t } = useI18n();

// 定义变量内容
const visible = ref(false);
let name = ref('');
let tableData = ref<any[]>([]);

// 打开弹窗
const openDialog = async ( barList: any) => {
	tableData.value = barList;
	visible.value = true;
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
