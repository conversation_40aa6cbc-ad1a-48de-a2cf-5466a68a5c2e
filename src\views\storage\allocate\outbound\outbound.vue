<template>
	<div class="layout-padding">
		<splitpanes>
			<pane size="15">
				<div class="layout-padding-auto layout-padding-view">
					<el-input v-model="state.queryForm.phone" placeholder="请通过条码扫码录入" clearable />
					<el-row class="mt3 mb5">
						<div class="w-full flex justify-between items-center">
							<div><span>读取标签数：</span><span class="text-blue-500">123</span></div>
							<div>
								<el-button text type="primary" icon="delete"> </el-button>
							</div>
						</div>
					</el-row>
					<ul class="overflow-auto h-[80vh]">
						<li v-for="i in count" :key="i" class="infinite-list-item">{{ i }}</li>
					</ul>
					<el-row class="mt12">
						<div class="w-full flex justify-evenly items-center">
							<el-button type="primary">读标签 </el-button>
							<el-button color="#FF7D00" type="primary" class="text-white">匹配 </el-button>
						</div>
					</el-row>
				</div>
			</pane>
			<!-- 右侧 -->
			<pane class="ml8">
				<div class="layout-padding-auto layout-padding-view">
					<el-row class="mt7">
						<div class="text-xl font-bold">出库</div>
					</el-row>

					<el-row class="mt20">
						<el-col :span="24">
							<Descriptions :column="4" :label-style="{ fontSize: '14px' }">
								<template #extra> </template>
								<DescriptionsItem label="出库单编号">{{}}</DescriptionsItem>
								<DescriptionsItem label="出库单状态">{{}}</DescriptionsItem>

								<DescriptionsItem label="出库仓库">{{}}</DescriptionsItem>
								<DescriptionsItem label="出库用途">{{}}</DescriptionsItem>
								<DescriptionsItem label="是否归还">{{}}</DescriptionsItem>
								<DescriptionsItem label="预计归还时间">{{}}</DescriptionsItem>
								<DescriptionsItem label="申请部门">{{}}</DescriptionsItem>
								<DescriptionsItem label="申请人员">{{}}</DescriptionsItem>
								<DescriptionsItem label="创建人">{{}}</DescriptionsItem>
								<DescriptionsItem label="创建时间">{{}}</DescriptionsItem>
								<DescriptionsItem label="出库人">{{}}</DescriptionsItem>
								<DescriptionsItem label="出库时间">{{}}</DescriptionsItem>
							</Descriptions>
						</el-col>
					</el-row>
					<el-table
						height="calc(100vh - 335px)"
						:data="tableData"
						border
						empty-text="暂无数据，请从左侧添加入库信息"
						:cell-style="tableStyle.cellStyle"
						:header-cell-style="tableStyle.headerCellStyle"
						ref="tableRefs"
						class="w-full"
					>
						<el-table-column label="序号" type="index" width="60" />
						<el-table-column label="物资编码" prop="a" show-overflow-tooltip width="200"></el-table-column>
						<el-table-column label="物资标识" prop="b" show-overflow-tooltip width="180"></el-table-column>
						<el-table-column label="物资名称" prop="c" show-overflow-tooltip width="320"></el-table-column>
						<el-table-column label="数量" prop="e" show-overflow-tooltip width="60"></el-table-column>
						<el-table-column label="单位" prop="e" show-overflow-tooltip width="60"></el-table-column>
						<el-table-column label="条码数量" prop="d" show-overflow-tooltip width="240"></el-table-column>
						<el-table-column label="条码明细" show-overflow-tooltip width="280" class-name="custom-class" fixed="right">
							<template #header>
								<div>
									<span>条码明细</span>
									<el-tooltip class="box-item" effect="light" content="全部收起" placement="top-start">
										<el-button
											v-if="tableData.some((row:any) => needsExpansion(row.barCode))"
											link
											type="primary"
											size="small"
											@click="toggleAllExpand"
										>
											<img :src="zdImg" v-show="isAllExpanded" />
										</el-button>
									</el-tooltip>
								</div>
							</template>
							<template #default="scope">
								<div v-if="scope.row.barCode">
									<div class="flex justify-items-start">
										<div>
											<div v-for="(code, index) in getDisplayCodes(scope.row)" :key="index">
												<span>{{ code }}</span>
												<span>&nbsp;&nbsp;&nbsp;</span>
												<el-button text type="primary" icon="delete" @click="delCode(scope.row, code)"> </el-button>
												<span>&nbsp;&nbsp;&nbsp;</span>
											</div>
										</div>

										<el-button
											v-if="needsExpansion(scope.row.barCode)"
											link
											type="primary"
											size="small"
											@click="toggleExpand(scope.row, scope.$index)"
										>
											<img :src="scope.row.isExpanded ? zdImg : zkImg" :alt="scope.row.isExpanded ? '收起' : '展开'" />
										</el-button>
									</div>
								</div>
							</template>
						</el-table-column>

					</el-table>
					<el-row>
						<div class="w-full mt15">
							<div class="float-left">物资清单（当前总数量 {{ tableData.reduce((sum: any, item: any) => sum + item.d, 0) }} ）</div>
							<div class="float-right">
								<el-button @click="returnClick">{{ $t('common.cancelButtonText') }}</el-button>
								<el-button @click="confirmClick" type="primary" :disabled="loading">确认出库</el-button>
							</div>
						</div>
					</el-row>
				</div>
			</pane>
		</splitpanes>

	</div>
</template>

<script lang="ts" name="systemUser" setup>
import {  pageList } from '/@/api/admin/user';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage} from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
// 动态引入组件

const { t } = useI18n();


// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		deptId: '',
		username: '',
		phone: '',
	},
	pageList: pageList,
	dataList: [],
});
const warnType = ref([
	{ label: '仓库1', value: 1 },
	{ label: '仓库2', value: 2 },
	{ label: '仓库3', value: 3 },
]);
const { getDataList, currentChangeHandle, sizeChangeHandle, tableStyle } = useTable(state);

//确认btn
const loading = ref(false);


//删除条码明细
const delCode = (row: any, code: any) => {
	tableData.value = tableData.value.map((item: any) =>
		item.barCode === row.barCode
			? {
					...item,
					barCode:
						item.barCode
							.split(',')
							.filter((barcode: string) => barcode !== code)
							.join(',') || '',
			  }
			: item
	);
};
//右侧提交
const confirmClick = () => {
	loading.value = true;
	setTimeout(() => {
		loading.value = false;
		useMessage().success('添加成功');
	}, 1000);
};

//返回上级菜单
const router = useRouter();
const returnClick = () => {
	router.replace({
		path: '/storage/allocate/outbound/index',
	});
};
const count = ref(['20230228000200022F020332', 2, 3, 4, 5, 6, 7, 8, 27, 75, 75]);
const tableData = ref<any>([]);

tableData.value = [
	{
		barCode: '20230228000100012F020002,20230228000100012F020003,20230228000100012F020004,20230228000100012F020005,20230228000100012F020006',
	},
	{
		barCode: '20230228000100012F020003',
	},
].map((item) => ({ ...item, isExpanded: false }));


// 表格内展开折叠
import zkImg from '/@/assets/flod.png';
import zdImg from '/@/assets/open.png';
const MAX_DISPLAY_LINES = 2;
//table ref
const tableRefs = ref();
//表格数据
//全部数据，截取最大行数   截取字段为row.barCode
const getDisplayCodes = (row: any) => {
	const barCode = row.barCode;
	if (!barCode) return [];
	const codes = barCode.split(',');
	if (codes.length > MAX_DISPLAY_LINES && !row.isExpanded) {
		return [...codes.slice(0, MAX_DISPLAY_LINES - 1)];
	}
	return codes;
};
//table滚动到指定行
const scrollToRow = (tableRef: any, rowIndex: number) => {
	nextTick(() => {
		if (tableRef.value) {
			// @ts-ignore
			const tableBody = tableRef.value.$el.querySelector('.el-table__body-wrapper');
			if (tableBody) {
				const row = tableBody.querySelectorAll('.el-table__row')[rowIndex - 1];
				if (row) {
					row.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
				}
			}
		}
	});
};
//将所有行 收起
const toggleAllExpand = () => {
	tableData.value.forEach((row: any) => {
		row.isExpanded = false;
	});
};
//有展开的行  则展示表头收起图标
const isAllExpanded = computed(() => tableData.value.some((row: any) => row.isExpanded));
//判断长度需要展开字段的数据长度
const needsExpansion = (barCode?: string) => {
	return barCode ? barCode.split(',').length > MAX_DISPLAY_LINES : false;
};
//行内展开收起事件
const toggleExpand = (row: any, rowIndex: any) => {
	row.isExpanded = !row.isExpanded;
	if (!row.isExpanded) scrollToRow(tableRefs, rowIndex + 1);
};



</script>
<style lang="scss"></style>
