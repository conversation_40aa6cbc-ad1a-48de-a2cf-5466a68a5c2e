<!-- 库存 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="w-full">
					<div class="float-left">
						<el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList" ref="queryRef">
							<el-form-item label="物资名称" prop="logType">
								<el-select placeholder="请选择" clearable v-model="state.queryForm.logType">
									<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in log_type" />
								</el-select>
							</el-form-item>
							<el-form-item label="库存量" prop="createTime">
								<div class="flex">
									<el-input-number v-model="state.queryForm.configName" hide-controls :min="0" controls-position="right" class="mr-1" />
									<span>—</span>
									<el-input-number v-model="state.queryForm.configNames" :min="state.queryForm.configName" controls-position="right" class="ml-1" />
								</div>
							</el-form-item>
							<el-form-item>
								<el-button @click="getDataList" icon="Search" type="primary">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>
					<!-- <div class="float-right">
						<el-button @click="getDataList"  type="primary">导出</el-button>
					</div> -->
				</div>
			</el-row>

			<el-table
				ref="tableRef"
				:data="state.dataList"
				@selection-change="handleSelectionChange"
				@sort-change="sortChangeHandle"
				v-loading="state.loading"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" />
				<el-table-column label="物资编码" prop="title" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资标识" prop="title" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资名称" prop="title" show-overflow-tooltip width="600"></el-table-column>
				<el-table-column label="仓库" prop="title" show-overflow-tooltip></el-table-column>
				<el-table-column label="当前库存" prop="title" show-overflow-tooltip></el-table-column>
				<el-table-column label="单位" prop="title" show-overflow-tooltip></el-table-column>
				<el-table-column label="库存预警值" prop="title" show-overflow-tooltip></el-table-column>
			</el-table>

			<pagination @current-change="currentChangeHandle" @size-change="sizeChangeHandle" v-bind="state.pagination"></pagination>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { BasicTableProps, useTable } from '/@/hooks/table';
import { delObj, pageList } from '/@/api/admin/log';
import { useI18n } from 'vue-i18n';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useDict } from '/@/hooks/dict';

const LogDetailRef = ref();

const { log_type } = useDict('log_type');

const { t } = useI18n();

// 定义变量内容
const queryRef = ref();
const showSearch = ref(true);

// 多选rows
const selectObjs = ref([]) as any;
// 是否可以多选
const multiple = ref(true);
let tableRef = ref(null);

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		logType: '',
		createTime: '',
		serviceId: '',
		configName: '',
		configNames: '',
	},
	selectObjs: [],
	pageList: pageList,
	descs: ['create_time'],
	createdIsNeed: false,
});

//  table hook
const { downBlobFile, getDataList, currentChangeHandle: baseCurrentChangeHandle, sortChangeHandle, sizeChangeHandle, tableStyle } = useTable(state);

// 分页事件
const currentChangeHandle = (page: number) => {
	// Reset table scroll position to top
	tableRef.value?.setScrollTop(0);
	// Call the original handler
	baseCurrentChangeHandle(page);
};

// 清空搜索条件
const resetQuery = () => {
	queryRef.value?.resetFields();
	getDataList();
};

// 导出excel
const exportExcel = () => {
	downBlobFile('/admin/log/export', state.queryForm, 'log.xlsx');
};

// 多选事件
const handleSelectionChange = (objs: { id: string }[]) => {
	selectObjs.value = objs.map(({ id }) => id);
	multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm(t('common.delConfirmText'));
	} catch {
		return;
	}

	try {
		await delObj(ids);
		getDataList();
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};

// onMounted 通过路由参数给  serviceId 赋值
const route = useRoute();
onMounted(() => {
	const { serviceId } = route.query;
	if (serviceId) {
		state.queryForm.serviceId = serviceId;
	}
	getDataList();
});
</script>

<style lang="scss" scoped>
pre code.hljs {
	width: 65%;
}
</style>
