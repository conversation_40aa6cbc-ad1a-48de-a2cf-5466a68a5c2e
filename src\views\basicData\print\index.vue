<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view pt-50 pl-20 box-border" >
			<el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="115px" v-loading="loading">
				<el-form-item :label="$t('print.printDevice')" prop="templateId">
					<el-select v-model="form.templateId" clearable class="w-60">
						<el-option v-for="item in printing_equipment" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
				</el-form-item>
				<el-form-item :label="$t('print.printerResolution')" prop="templateId">
					<el-select v-model="form.templateId" clearable class="w-60">
						<el-option v-for="item in print_resolution" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
				</el-form-item>
				<el-form-item :label="$t('print.printerManufacturer')" prop="templateId">
					<el-select v-model="form.templateId" clearable class="w-60">
						<el-option v-for="item in printer_manufacturers" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
				</el-form-item>
				<el-form-item>
					<el-button @click="onSubmits" type="primary" :disabled="loading">{{ $t('common.confirmButtonText') }}</el-button>
				</el-form-item>
			</el-form>
		</div>
	</div>
</template>

<script setup lang="ts" name="systemSysMessage">
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { getObj } from '/@/api/basicData/print';
import { useDict } from '/@/hooks/dict';

//引入字典
const { printer_manufacturers, print_resolution, printing_equipment } = useDict('printer_manufacturers', 'print_resolution', 'printing_equipment');
// 引入组件
const { t } = useI18n();


// 定义变量内容
const dataFormRef = ref();
const loading = ref(false);

// 提交表单数据
const form = reactive({
	id: '',
	groupName: '',
	sortOrder: '',
	templateId: [] as string[],
});

// 定义校验规则
const dataRules = ref({
	groupName: [{ required: true, message: '请选择', trigger: 'blur' }],
	templateId: [{ required: true, message: '请选择', trigger: 'blur' }],
});

//获取详情
const getFormDetails = () => {
	getObj().then((res: any) => {
		Object.assign(form, res.data);
	});
};

// 提交
const onSubmits = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;
	try {
		loading.value = true;
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};

//生命周期
onMounted(() => {
	getFormDetails();
});
</script>
