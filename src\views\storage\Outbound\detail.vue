<template>
	<el-drawer v-model="visible" size="30%" :with-header="false">
		<div class="w-full">
			<el-row>
				<div class="ml-7">
					<span class="mr-3"> 物资名称： </span>
					<span> jsdakdjska </span>
				</div>
			</el-row>
			<el-row class="mb-2 mt-2">
				<div>
					<span class="ml-7 mr-3"> 物资编码： </span>
					<span> jsdakdjska </span>
				</div>
			</el-row>
			<el-row>
				<div  class="w-full  flex justify-between items-center">
					<div class="ml-7">
						<span class="mr-3"> 物资标识： </span>
						<span> jsdakdjska </span>
					</div>
					<div>
						<!-- <el-button type="primary" @click="">打印标签</el-button> -->
					</div>
				</div>
			</el-row>
			<el-row class="mb8"> </el-row>
			<el-table
				:data="tableData"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
				max-height="calc(100vh - 150px)"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="物资条码" prop="a" show-overflow-tooltip width="480"></el-table-column>
				<!-- <el-table-column label="状态" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="操作" fixed="right">
					<template #default="scope">
						<el-button v-auth="'sys_user_edit'" text type="primary" @click=""> 写标签 </el-button>
					</template>
				</el-table-column> -->
			</el-table>
		</div>
	</el-drawer>
</template>

<script setup lang="ts" name="log-detail">
import { addObj, getObj, putObj, validatePhone, validateUsername } from '/@/api/admin/user';
import { list as roleList } from '/@/api/admin/role';
import { list as postList } from '/@/api/admin/post';
import { deptTree } from '/@/api/admin/dept';
import { useDict } from '/@/hooks/dict';
import { useI18n } from 'vue-i18n';
import { useMessage } from '/@/hooks/message';
import { BasicTableProps, useTable } from '/@/hooks/table';

const state: BasicTableProps = reactive<BasicTableProps>({});
const { tableStyle } = useTable(state);

const { t } = useI18n();

// 定义刷新表格emit
const emit = defineEmits(['refresh']);
// @ts-ignore
const { lock_flag } = useDict('lock_flag');

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const deptData = ref<any[]>([]);
const roleData = ref<any[]>([]);
const postData = ref<any[]>([]);
const loading = ref(false);
const tableData = ref([
	{ a: 1 },
	{ a: 1 },
	{ a: 1 },
	{ a: 1 },
	{ a: 1 },
	{ a: 1 },
	{ a: 1 },
	{ a: 1 },
	{ a: 1 },
	{ a: 1 },
	{ a: 1 },
	{ a: 1 },
	{ a: 1 },
	{ a: 1 },
	{ a: 1 },
	{ a: 1 },
	{ a: 1 },
	{ a: 1 },
	{ a: 1 },
	{ a: 1 },
	{ a: 1 },
	{ a: 1 },
]);
const props = defineProps({
	deptId: {
		type: String,
		default: '',
	},
});

const dataForm = reactive({
	userId: '',
	username: '',
	password: '' as String | undefined,
	salt: '',
	wxOpenid: '',
	qqOpenid: '',
	lockFlag: '0',
	phone: '' as String | undefined,
	deptId: '',
	roleList: [],
	postList: [],
	nickname: '',
	name: '',
	email: '',
	post: [] as string[],
	role: [] as string[],
});

// 打开弹窗
const openDialog = async (id: any) => {
	visible.value = true;

	// 加载使用的数据
	// getDeptData();
	// getPostData();
	// getRoleData();
};

/**
 * 从服务器获取用户数据
 *
 * @async
 * @param {string} id - 用户 ID
 * @return {Promise} - 包含用户数据的 Promise 对象
 */
const getUserData = async (id: string) => {
	try {
		loading.value = true;
		const { data } = await getObj(id);
		Object.assign(dataForm, data);
		if (data.roleList) {
			dataForm.role = data.roleList.map((item) => item.roleId);
		}
		if (data.postList) {
			dataForm.post = data.postList.map((item) => item.postId);
		}
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};

// 初始化部门数据
const getDeptData = () => {
	// 获取部门数据
	deptTree().then((res) => {
		deptData.value = res.data;
		// 默认选择在树中选中的部门
		if (!dataForm.userId) {
			dataForm.deptId = props.deptId;
		}
	});
};

// 岗位数据
const getPostData = () => {
	postList().then((res) => {
		postData.value = res.data;
		// 默认选择第一个
		if (!dataForm.userId) {
			dataForm.post = [res.data[0].postId];
		}
	});
};
// 角色数据
const getRoleData = () => {
	roleList().then((res) => {
		roleData.value = res.data;
		// 默认选择第一个
		if (!dataForm.userId) {
			dataForm.role = [res.data[0].roleId];
		}
	});
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
