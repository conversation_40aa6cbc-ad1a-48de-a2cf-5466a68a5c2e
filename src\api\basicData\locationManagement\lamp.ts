// 分区管理

import request from '/@/utils/request';
//仓库下拉
export const getWarehouse = () => {
	return request({
		url: '/admin/warehouse/selectWarehouseList',
		method: 'get',
	});
};
//区域下拉
export const getArea = (id: string) => {
	return request({
		url: '/admin/warehouseZone/selectWarehouseZoneList/' + id,
		method: 'get',
	});
};
//列下拉
export const getColumn = (id: string) => {
	return request({
		url: '/admin/warehouseLocation/selectWarehouseShelfList/' + id,
		method: 'get',
	});
};
//组下拉
export const getGroup = (data?: Object) => {
	return request({
		url: '/admin/warehouseLocation/selectWarehouseGroupList',
		method: 'post',
		data,
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
};
//层下拉
export const getLayer = (data?: Object) => {
	return request({
		url: '/admin/warehouseLocation/selectWarehouseLevelList',
		method: 'post',
		data,
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
};
export const pageList = (data?: Object) => {
	return request({
		url: '/admin/warehouseLocation/getWareLocaShelfIndicatorPage',
		method: 'post',
		data,
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
};

export function addObj(obj?: Object) {
	return request({
		url: '/admin/wareLocaShelfIndicator/bindShelfIndicator',
		method: 'post',
		data: obj,
	});
}

export function delObj(obj?: Object) {
	return request({
		url: '/admin/wareLocaShelfIndicator/unbindShelfIndicator',
		method: 'post',
		data: obj,
	});
}

//定位
export function locateLight(obj?: Object) {
	return request({
		url: '/admin/shelfIndicator/locateLight',
		method: 'post',
		data: obj,
	});
}

export function closeObj() {
	return request({
		url: '/admin/shelfIndicator/locateLightOff',
		method: 'get',
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
}
