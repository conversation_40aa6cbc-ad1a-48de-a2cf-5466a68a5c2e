<!-- 物资类别 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full">
					<div class="float-right">
						<el-button icon="folder-add" type="primary" class="top-right-btn" v-if="!defaultTreeViewRef" v-auth="'sys_dept_add'" @click="handleAdd">
							{{ $t('common.addBtn') }}
						</el-button>
						<el-button @click="handleExpand"> {{ $t('common.expandBtn') }}</el-button>
					</div>
				</div>
			</el-row>
			<table-view ref="tableViewRef" v-if="!defaultTreeViewRef" />
			<upload-excel
				ref="excelUploadRef"
				:title="$t('sysdept.importTip')"
				url="/admin/dept/import"
				temp-url="/admin/sys-file/local/file/dept.xlsx"
				@refreshDataList="getDataList"
			/>
		</div>
	</div>
</template>

<script lang="ts" name="systemDept" setup>
import { useI18n } from 'vue-i18n';

const TableView = defineAsyncComponent(() => import('./table-view.vue'));

const { t } = useI18n();

// 默认树视图展示
const defaultTreeViewRef = ref(false);
const treeViewRef = ref();
const tableViewRef = ref();
const excelUploadRef = ref();

const state = reactive({
	queryForm: {
		deptName: '',
	},
});

const handleAdd = () => {
	tableViewRef.value.handleAdd();
};

/**
 * 处理展开/折叠树
 */
const handleExpand = () => {
	if (defaultTreeViewRef.value) {
		treeViewRef.value.handleExpand();
	} else {
		tableViewRef.value.handleExpand();
	}
};

const getDataList = () => {
	if (defaultTreeViewRef.value) {
		treeViewRef.value.getOrgData();
	} else {
		tableViewRef.value.getDataList();
	}
};
</script>
