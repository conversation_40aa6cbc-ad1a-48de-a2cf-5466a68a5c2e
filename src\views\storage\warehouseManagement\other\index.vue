<!-- 场景预案 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full" >
					<div class="float-left">
						<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
							<el-form-item label="入库单编号">
								<el-input v-model="state.queryForm.billCode" placeholder="请输入" clearable style="max-width: 180px" />
							</el-form-item>
							<el-form-item label="入库单状态">
								<el-select placeholder="请选择" clearable v-model="state.queryForm.billStatus">
									<el-option
										:key="item.value"
										:label="item.label"
										:value="item.value"
										v-for="item in [
											{ label: '待入库', value: 0 },
											{ label: '已入库', value: 1 },
											{ label: '已上架', value: 2 },
										]"
									/>
								</el-select>
							</el-form-item>
							<el-form-item label="入库仓库">
								<el-select placeholder="请选择" clearable v-model="state.queryForm.warehouseId">
									<el-option :key="item.value" :label="item.warehouseName" :value="item.id" v-for="item in warehouseData" />
								</el-select>
							</el-form-item>
							<el-form-item label="入库时间" prop="time">
								<el-date-picker
									:end-placeholder="$t('syslog.inputEndPlaceholderTip')"
									:start-placeholder="$t('syslog.inputStartPlaceholderTip')"
									range-separator="To"
									type="datetimerange"
									@change="dateChange"
									v-model="state.queryForm.time"
									value-format="YYYY-MM-DD HH:mm:ss"
								/>
							</el-form-item>

							<el-form-item>
								<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>
					<div class="float-right">
						<el-button v-auth="'sys_user_add'" icon="folder-add" type="primary" @click="routerClick">
							{{ $t('common.addBtn') }}
						</el-button>
					</div>
				</div>
			</el-row>

			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				@selection-change="handleSelectionChange"
				row-key="userId"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>

				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="入库单编号" prop="billCode" show-overflow-tooltip></el-table-column>
				<el-table-column label="入库单状态" prop="name" show-overflow-tooltip>
					<template #default="scope">
						{{ inventoryStatus[scope.row.billStatus] }}
					</template>
				</el-table-column>
				<el-table-column label="入库仓库" prop="warehouseName" show-overflow-tooltip></el-table-column>
				<el-table-column label="入库部门" prop="entryDept" show-overflow-tooltip></el-table-column>
				<el-table-column label="入库人员" prop="entryUser" show-overflow-tooltip></el-table-column>
				<el-table-column label="入库时间" prop="entryTime" show-overflow-tooltip></el-table-column>
				<el-table-column label="上架人员" prop="putShelfUser" show-overflow-tooltip></el-table-column>
				<el-table-column label="上架时间" prop="putShelfTime" show-overflow-tooltip></el-table-column>
				<el-table-column :label="$t('common.action')" width="200" fixed="right">
					<template #default="scope">
						<!-- 上架指引-->
						<el-button v-auth="'sys_user_edit'" text type="primary" @click="guideClick(scope.row.id)"> 上架指引 </el-button>
						<!-- 查看 入库单 -->
						<el-button v-auth="'sys_user_edit'" text type="primary" @click="formClick(scope.row.userId)"> 查看 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"> </pagination>
		</div>
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { delObj, pageList, getWarehouse } from '/@/api/storage/warehouseManagement/other';

import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { useDict } from '/@/hooks/dict';

// 定义查询字典
const { warnType } = useDict('warnType');
// 动态引入组件

const { t } = useI18n();

// 定义变量内容
const userDialogRef = ref();
const queryRef = ref();

const param_type = ref([]);
// 多选rows
const selectObjs = ref([]) as any;
// 是否可以多选
const multiple = ref(true);

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		billCode: '',
		billType: '',
		warehouseId: '',
		time: '',
		beginEntryTime: '',
		endEntryTime: '',
	},
	pageList: pageList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);
const inventoryStatus: any = {
	'0': '待入库',
	'1': '已入库',
	'2': '已上架',
};
// 多选事件
const handleSelectionChange = (objs: { userId: string }[]) => {
	selectObjs.value = objs.map(({ userId }) => userId);
	multiple.value = !objs.length;
};
const dateChange = (value: any) => {
	state.queryForm.beginEntryTime = '';
	state.queryForm.endEntryTime = '';
	if (!Array.isArray(value)) return;
	state.queryForm.beginEntryTime = value[0];
	state.queryForm.endEntryTime = value[1];
};



//查看入库单页面
const formClick = (id?: any) => {
	router.push({
		path: '/storage/warehouseManagement/other/form',
		query: { id: id, notCreateTags: 'true' },
	});
};
//上架指引
const guideClick = (id?: any) => {
	router.push({
		path: '/storage/warehouseManagement/other/guide',
		query: { id: id, notCreateTags: 'true' },
	});
};


// 获取仓库数据
const warehouseData = ref<any[]>([]);
const getWarehouseData = () => {
	getWarehouse().then((res: any) => {
		warehouseData.value = res.data;
	});
};
//新增 页面
const router = useRouter();
const routerClick = (id?: any) => {
	const tagsViewName = id ? `修改:${id}` : '新增';
	router.push({
		path: '/storage/warehouseManagement/other/add',
		query: { id: id, tagsViewName: tagsViewName, notCreateTags: 'true' },
	});
};
onMounted(() => {
	getWarehouseData();
});
</script>
