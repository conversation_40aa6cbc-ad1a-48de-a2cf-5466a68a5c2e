<template>
	<div>
		<el-row>
			<div class="mb8 w-full">
				<div class="float-left">
					<el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
						<el-form-item label="所属仓库" prop="warehouseId">
							<el-select placeholder="" v-model="state.queryForm.warehouseId" style="max-width: 180px" clearable @change="getZoneCodeSelect">
								<el-option :key="index" :label="item.warehouseName" :value="item.id" v-for="(item, index) in warehouseData"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="区号" prop="zoneCode">
							<el-select placeholder="" v-model="state.queryForm.warehouseZoneId" style="max-width: 180px" clearable @change="getColumnSelect">
								<el-option :key="index" :label="item.zoneName" :value="item.id" v-for="(item, index) in zoneCodeData"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="列号" prop="shelfCode">
							<el-select placeholder="" v-model="state.queryForm.shelfCode" style="max-width: 180px" clearable>
								<el-option :key="index" :label="item.shelfCode" :value="item.shelfCode" v-for="(item, index) in columnCodeData"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="位置编码" prop="locationCode">
							<el-input placeholder="请输入" v-model="state.queryForm.locationCode" clearable style="min-width: 240px" />
						</el-form-item>
						<el-form-item>
							<el-button icon="search" type="primary" @click="getDataList"> 查询 </el-button>
						</el-form-item>
					</el-form>
				</div>
				<div class="float-right">
					<el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()" > 新 增 </el-button>
					<el-button type="primary" class="ml10" @click="autoBindingClick" > 自动绑定 </el-button>
					<el-button type="primary" class="ml10" @click="printLabelClick" > 打印标签 </el-button>
				</div>
			</div>
		</el-row>
		<el-table
			:data="state.dataList"
			v-loading="state.loading"
			border
			:cell-style="tableStyle.cellStyle"
			:header-cell-style="tableStyle.headerCellStyle"
			@selection-change="selectionChangHandle"
			@sort-change="sortChangeHandle"
			max-height="620px"

		>
			<el-table-column type="selection" width="40" align="center" />
			<el-table-column type="index" label="序号" width="60" />
			<el-table-column prop="warehouseName" label="仓库" show-overflow-tooltip />
			<el-table-column prop="zoneCode" label="区号" show-overflow-tooltip />
			<el-table-column prop="shelfCode" label="列号" show-overflow-tooltip />
			<el-table-column prop="locationCode" label="位置编码" show-overflow-tooltip />
			<el-table-column prop="locationBar" label="位置条码" show-overflow-tooltip />
			<el-table-column prop="mac" label="标签mac" show-overflow-tooltip />
			<el-table-column label="操作" width="300">
				<template #default="scope">
					<el-button icon="edit-pen" text type="primary" v-auth="'sys_message_edit'" @click="formDialogRef.openDialog(scope.row.warehouseLocationId)">绑定 </el-button>
					<el-button icon="delete" text type="primary" v-auth="'sys_message_del'" @click="handleDelete(scope.row.warehouseLocationId)">删除 </el-button>
					<el-button icon="Tickets" text type="primary" v-auth="'sys_message_del'" @click="handleLog(scope.row.mac)">定位 </el-button>
				</template>
			</el-table-column>
		</el-table>
		<pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
	</div>
</template>

<script setup lang="ts" name="systemSmsMessage">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { delObj, pageList, getArea, getColumn, getWarehouse,autoBind,locateLight } from '/@/api/basicData/locationManagement/label';
import { useMessage, useMessageBox } from '/@/hooks/message';

// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));
// 定义变量内容
const formDialogRef = ref();
// 搜索变量
const queryRef = ref();
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		warehouseId: '',
		warehouseZoneId: '',
		shelfCode: '',
		locationCode: '',
	},
	pageList,
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, tableStyle } = useTable(state);

// 多选事件
const selectionChangHandle = (objs: { warehouseId: string; warehouseZoneId: string; shelfCode: string; locationCode: string }[]) => {
	selectObjs.value = objs.map(({ warehouseId, warehouseZoneId, shelfCode, locationCode }) => ({
		warehouseId,
		warehouseZoneId,
		shelfCode,
		locationCode,
	}));
	multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (id: string) => {
	try {
		await useMessageBox().confirm('此操作将永久删除');
	} catch {
		return;
	}

	try {
		await delObj(id);
		getDataList();
		useMessage().success('删除成功');
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};

const handleLog = async (mac:string) => {
	try {
		await locateLight([mac]);
		useMessage().success('定位成功');
	} catch (err: any) {
		useMessage().error(err.msg);
	}


};
let warehouseData = ref<any[]>([]);
let zoneCodeData = ref<any[]>([]);
let columnCodeData = ref<any[]>([]);
const getWarehouseSelect = async () => {
	warehouseData.value = (await getWarehouse())?.data || [];
};
const getZoneCodeSelect = async (id: string) => {
	state.queryForm.warehouseZoneId = '';
	if (!id) return;
	zoneCodeData.value = (await getArea(id))?.data || [];
};
const getColumnSelect = async (id: string) => {
	state.queryForm.shelfCode = '';
	if (!id) return;
	columnCodeData.value = (await getColumn(id))?.data || [];
};


const autoBindingClick = async () => {
	try {
		await useMessageBox().confirm('此操作将自动绑定');
	} catch {
		return;
	}
	try {
		await autoBind({warehouseId:state.queryForm.warehouseId,warehouseZoneId:state.queryForm.warehouseZoneId,shelfCode:state.queryForm.shelfCode,locationCode:state.queryForm.locationCode});
		getDataList();
		useMessage().success('绑定成功');
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};
const printLabelClick = async () => {

};
onMounted(() => {
	getWarehouseSelect();
});
</script>
