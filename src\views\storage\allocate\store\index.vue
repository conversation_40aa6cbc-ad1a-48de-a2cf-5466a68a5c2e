<!-- 场景预案 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full" >
					<div class="float-left">
						<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
							<el-form-item label="调拨申请单编号" prop="transferBillCode">
								<el-input v-model="state.queryForm.transferBillCode" placeholder="请输入" clearable class="!max-w-[180px]" />
							</el-form-item>
							<el-form-item label="入库单编号" prop="entryBillCode">
								<el-input v-model="state.queryForm.entryBillCode" placeholder="请输入" clearable class="!max-w-[180px]" />
							</el-form-item>
							<el-form-item label="入库单状态" prop="entryBillStatus">
								<el-select placeholder="请选择" clearable v-model="state.queryForm.entryBillStatus"  class="!w-[160px]" >
									<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in [{label:'待入库',value:'0'},{label:'已入库',value:'1'},{label:'已上架',value:'2'}]" />
								</el-select>
							</el-form-item>
							<el-form-item label="申请部门" prop="applyDeptId">
								<el-select placeholder="请选择" clearable v-model="state.queryForm.applyDeptId" class="!w-[160px]">
									<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in warnType" />
								</el-select>
							</el-form-item>
							<el-form-item label="申请人员" prop="applyUserId">
								<el-select placeholder="请选择" clearable v-model="state.queryForm.applyUserId" class="!w-[160px]" >
									<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in warnType" />
								</el-select>
							</el-form-item>


							<el-form-item>
								<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>

				</div>
			</el-row>

			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				@selection-change="handleSelectionChange"
				row-key="userId"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />

				<el-table-column label="调拨申请单编号" prop="transferBillCode" show-overflow-tooltip></el-table-column>
				<el-table-column label="入库单编号" prop="entryBillCode" show-overflow-tooltip></el-table-column>
				<el-table-column label="入库单状态"  show-overflow-tooltip>
					<template #default="scope">
					{{ warehousingEntry[scope.row.billStatus] }}
					</template>
				</el-table-column>
				<el-table-column label="入库仓库" prop="warehouseName" show-overflow-tooltip></el-table-column>
				<el-table-column label="入库时间" prop="entryTime" show-overflow-tooltip></el-table-column>
				<el-table-column label="入库部门" prop="entryDept" show-overflow-tooltip></el-table-column>
				<el-table-column label="入库人员" prop="entryUser" show-overflow-tooltip></el-table-column>

				<el-table-column label="上架人员" prop="putShelfUser" show-overflow-tooltip></el-table-column>
				<el-table-column label="上架时间" prop="putShelfTime" show-overflow-tooltip></el-table-column>

				<el-table-column :label="$t('common.action')" width="200" fixed="right">
					<template #default="scope">
						<!-- 核验入库 verification-->
						<el-button v-auth="'sys_user_edit'" icon="" text type="primary" @click="confirmClick(scope.row.userId)"> 核验入库 </el-button>


						<!-- 查看  form-->
						<el-button v-auth="'sys_user_edit'" text type="primary" @click="formClick(scope.row.userId)"> 查看 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"> </pagination>
		</div>
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { delObj, pageList, putObj } from '/@/api/admin/user';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { useDict } from '/@/hooks/dict';

// 定义查询字典
const { warnType } = useDict('warnType');
// 动态引入组件

const { t } = useI18n();

// 定义变量内容
const userDialogRef = ref();
const queryRef = ref();

const param_type = ref([]);
// 多选rows
const selectObjs = ref([]) as any;
// 是否可以多选
const multiple = ref(true);

// 定义表格查询、后台调用的API
const warehousingEntry: any	={
	'0': '待入库',
	'1': '已入库',
	'2': '已上架'
}
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		transferBillCode: '',
		entryBillCode: '',
		entryBillStatus: '',
		applyDeptId: '',
		applyUserId: '',

	},
	pageList: pageList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 多选事件
const handleSelectionChange = (objs: { userId: string }[]) => {
	selectObjs.value = objs.map(({ userId }) => userId);
	multiple.value = !objs.length;
};

//表格内开关 (用户状态)
const changeSwitch = async (row: any) => {
	// 不修改密码
	row.password = undefined;
	row.phone = undefined;
	await putObj(row);
	useMessage().success(t('common.optSuccessText'));
	getDataList();
};
// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm(t('common.delConfirmText'));
	} catch {
		return;
	}

	try {
		await delObj(ids);
		getDataList();
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};

//查看
const formClick = (id?: any) => {
	router.push({
		path: '/storage/allocate/store/form',
		query: { id: id, notCreateTags: 'true' },
	});
};

//核验入库
const confirmClick = (id?: any) => {
	router.push({
		path: '/storage/allocate/store/verification',
		query: { id: id, notCreateTags: 'true' },
	});
};

//新增 修改页面
const router = useRouter();
const routerClick = (id?: any) => {
	const tagsViewName = id ? `场景预案修改:${id}` : '场景预案新增';
	router.push({
		path: '/storage/Outbound/add',
		query: { id: id, tagsViewName: tagsViewName, notCreateTags: 'true' },
	});
};
</script>
