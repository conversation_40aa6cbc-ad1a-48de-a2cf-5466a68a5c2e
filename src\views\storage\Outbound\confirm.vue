<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row class="mt20 mb-2">
				<el-col :span="24">
					<Descriptions title="" :column="4" :label-style="{ fontSize: '14px' }">
						<template #extra> </template>
						<DescriptionsItem label="出库单编号">{{}}</DescriptionsItem>
						<DescriptionsItem label="出库单状态">{{}}</DescriptionsItem>
						<DescriptionsItem label="出库仓库">{{}}</DescriptionsItem>
						<DescriptionsItem label="出库用途">{{}}</DescriptionsItem>
						<DescriptionsItem label="是否归还">{{}}</DescriptionsItem>
						<DescriptionsItem label="预计归还时间">{{}}</DescriptionsItem>
						<DescriptionsItem label="申请部门">{{}}</DescriptionsItem>
						<DescriptionsItem label="申请人员">{{}}</DescriptionsItem>
						<DescriptionsItem label="创建人">{{}}</DescriptionsItem>
						<DescriptionsItem label="创建时间">{{}}</DescriptionsItem>
						<DescriptionsItem label="出库人">{{}}</DescriptionsItem>
						<DescriptionsItem label="出库时间">{{}}</DescriptionsItem>
					</Descriptions>
				</el-col>
			</el-row>

			<el-row>
				<div class="mb-2 font-bold" >出库物资清单</div>
			</el-row>
			<el-table
				v-loading="state.loading"
				:data="tableData"
				max-height="calc(100vh - 355px)"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
				ref="tableRefs"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="物资编码" prop="username" fixed="left" show-overflow-tooltip></el-table-column>

				<el-table-column label="物资标识" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资名称" prop="name" show-overflow-tooltip width="600"></el-table-column>
				<el-table-column label="数量" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="单位" prop="name" show-overflow-tooltip></el-table-column>
			</el-table>
			<el-row  class="fixed bottom-[15px] right-[20px]">
				<el-button @click="returnClick">取消</el-button>
				<el-button type="primary">确认</el-button>
			</el-row>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { delObj, pageList, putObj } from '/@/api/admin/user';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';

// 动态引入组件

const { t } = useI18n();

// 多选rows
// 是否可以多选

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		deptId: '',
		username: '',
		phone: '',
	},
	pageList: pageList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

const router = useRouter();
const returnClick = () => {
	router.replace({ path: '/storage/Outbound/index' });
};

const tableData = ref<any>([]);
tableData.value = [
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
	{ a: '1' },
];
</script>

<style lang="scss" scoped>
pre code.hljs {
	width: 65%;
}
</style>
