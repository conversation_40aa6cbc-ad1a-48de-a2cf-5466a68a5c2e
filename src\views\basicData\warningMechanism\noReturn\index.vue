<template>
	<div>
		<div class="layout-padding-auto layout-padding-view">
			<el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="95px" v-loading="loading">
				<el-form-item :label="$t('sysrole.templateType')" prop="warningMethod">
					<el-select v-model="form.warningMethod" :placeholder="$t('sysrole.selectType')" clearable multiple class="w-80">
						<el-option v-for="item in warning_method" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
				</el-form-item>
				<el-form-item :label="$t('sysrole.beforeTheReturnTime')" prop="returnWarningValue">
					<div class="flex">
						<div>
							<el-input v-model="form.returnWarningValue" class="w-20" />
						</div>
						<div class="ml-2">天提醒</div>
					</div>
				</el-form-item>
				<el-form-item>
					<el-button @click="onSubmits" type="primary" :disabled="loading">{{ $t('common.confirmButtonText') }}</el-button>
				</el-form-item>
			</el-form>
		</div>
	</div>
</template>

<script setup lang="ts" name="systemSysMessage">
import { putUnreturnedObj, getUnreturnedObj } from '/@/api/basicData/warningMechanism';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { useDict } from '/@/hooks/dict';

const { warning_method } = useDict('warning_method');
// 引入组件
const { t } = useI18n();

// 定义变量内容
const dataFormRef = ref();
const loading = ref(false);

// 提交表单数据
const form = ref<any>({
	warningMethod: null,
	returnWarningValue: null,
});

// 定义校验规则
const dataRules = ref({
	warningMethod: [{ required: true, message: '请选择通知方式', trigger: 'blur' }],
	returnWarningValue: [
		{
			required: true,
			message: '请输入',
			trigger: 'blur',
		},
		{
			pattern: /^[0-9]+$/,
			message: '值只能是数字',
			trigger: 'blur',
		},
	],
});
//获取数据
const getInfos = async () => {
	try {
		loading.value = true;
		let res = await getUnreturnedObj();
		form.value.warningMethod = String(res.data.warningMethod).split(',');
		form.value.returnWarningValue = String(res.data.returnWarningValue);
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};
// 提交
const onSubmits = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;

	try {
		loading.value = true;
		await putUnreturnedObj({ ...form.value });
		useMessage().success('操作成功');
		getInfos();
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};
onMounted(() => {
	getInfos();
});
</script>
