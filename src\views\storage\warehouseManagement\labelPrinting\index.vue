<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full" >
					<div class="float-left">
						<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
							<el-form-item label="仓库" prop="logType">
								<el-select placeholder="请选择" clearable v-model="state.queryForm.logType">
									<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in warnType" />
								</el-select>
							</el-form-item>
							<el-form-item label="物资名称" prop="phone">
								<el-input v-model="state.queryForm.phone" placeholder="请输入" clearable style="max-width: 180px;" />
							</el-form-item>
							<el-form-item label="物资条码" prop="createTime">
								<div class="flex">
									<el-input v-model="state.queryForm.phone" placeholder="例：20250215000100012F000100" clearable  class="mr-1 w-[240px]" />

									<span>—</span>
									<el-input v-model="state.queryForm.phone" placeholder="20250215000100012F000120" clearable  class="ml-1 w-[240px]" />
								</div>
							</el-form-item>
							<el-form-item>
								<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>
					<div class="float-right">
						<el-button
							:disabled="multiple"
							@click="handleDelete(selectObjs)"
							class="ml10"
							formDialogRef
							icon="printer"
							type="primary"
							v-auth="'sys_i18n_del'"
						>
							打印标签
						</el-button>
					</div>
				</div>
			</el-row>

			<el-table
				:data="state.dataList"
				@selection-change="handleSelectionChange"
				@sort-change="sortChangeHandle"
				class="w-full"
				row-key="id"
				v-loading="state.loading"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column align="center" type="selection" width="40" />
				<el-table-column label="序号" type="index" width="60" />
				<el-table-column label="物资编码" prop="name" show-overflow-tooltip />
				<el-table-column label="物资标识" prop="name" show-overflow-tooltip />
				<el-table-column label="物资名称" prop="name" show-overflow-tooltip width="570" />
				<el-table-column label="条码" prop="name" show-overflow-tooltip />
				<el-table-column :label="$t('common.action')" width="150">
					<template #default="scope">
						<el-button icon="edit-pen" @click="" text type="primary" v-auth="'sys_i18n_edit'">写标签 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination @current-change="currentChangeHandle" @size-change="sizeChangeHandle" v-bind="state.pagination" />
		</div>

		<!-- 编辑、新增  -->
	</div>
</template>

<script lang="ts" name="systemSysI18n" setup>
import { BasicTableProps, useTable } from '/@/hooks/table';
import { delObj, fetchList, refreshCache } from '/@/api/admin/i18n';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';

// 引入组件
const { t } = useI18n();
// 定义查询字典

// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		name: '',
		zhCn: '',
		en: '',
	},
	pageList: fetchList,
	descs: ['create_time'],
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value.resetFields();
	// 清空排序规则
	state.queryForm!.descs = [];
	state.queryForm!.ascs = [];
	// 清空多选
	selectObjs.value = [];
	getDataList();
};
const handleRefreshCache = () => {
	refreshCache().then(() => {
		useMessage().success('同步成功');
	});
};

// 导出excel
const exportExcel = () => {
	downBlobFile('/admin/i18n/export', Object.assign(state.queryForm, { ids: selectObjs }), 'i18n.xlsx');
};

// 多选事件
const handleSelectionChange = (objs: { id: string }[]) => {
	selectObjs.value = objs.map(({ id }) => id);
	multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm(t('common.delConfirmText'));
	} catch {
		return;
	}

	try {
		await delObj(ids);
		getDataList();
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};
</script>
