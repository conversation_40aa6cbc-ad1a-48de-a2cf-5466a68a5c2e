<template>
	<div class="layout-padding">
		<splitpanes>
			<pane size="42">
				<div class="layout-padding-auto layout-padding-view">
					<el-tabs v-model="activeName" @tab-click="handleClick">
						<el-tab-pane label="单物资" lazy name="singleMaterial">
							<template #label>
								<div class="ml-0">单物资</div>
							</template>
							<material-single @add-to-right="addToRightList" />
						</el-tab-pane>
					</el-tabs>
				</div>
			</pane>
			<!-- 右侧 -->
			<pane class="ml8">
				<div class="layout-padding-auto layout-padding-view">
					<el-row class="mt7">
						<div class="text-xl font-bold">新增物资组合</div>
					</el-row>
					<el-row class="mt20 mb16">
						<el-form ref="queryRef" :inline="false" :model="form" :rules="formRules">
							<el-form-item label="物资组合名称" prop="matingGroupName">
								<el-input v-model="form.matingGroupName" placeholder="请输入物资组合名称" clearable style="max-width: 180px" />
							</el-form-item>
							<el-form-item label="启用状态" prop="status">
								<el-radio-group v-model="form.status">
									<el-radio :key="index" :label="item.value" border v-for="(item, index) in enable_status">{{ item.label }} </el-radio>
								</el-radio-group>
							</el-form-item>
						</el-form>
					</el-row>
					<el-table
						height="calc(100vh - 290px)"
						:data="rightDataList"
						border
						empty-text="暂无数据，请从左侧添加物资信息"
						:cell-style="tableStyle.cellStyle"
						:header-cell-style="tableStyle.headerCellStyle"
					>
						<el-table-column label="序号" type="index" width="60" fixed="left" />
						<el-table-column label="物资编码" prop="materialCode" fixed="left" show-overflow-tooltip></el-table-column>
						<el-table-column label="物资标识" prop="materialIdentify" show-overflow-tooltip></el-table-column>
						<el-table-column label="物资名称" prop="materialName" show-overflow-tooltip width="200"></el-table-column>
						<el-table-column label="数量" prop="materialNum" show-overflow-tooltip width="60"></el-table-column>
						<el-table-column label="单位" prop="basicUnit" show-overflow-tooltip width="60"></el-table-column>
						<el-table-column :label="$t('common.action')" fixed="right" width="60">
							<template #default="scope">
								<el-button v-auth="'sys_user_edit'" text type="primary" @click="deleteClick(scope.row)"> 删除 </el-button>
							</template>
						</el-table-column>
					</el-table>
					<el-row>
						<div class="w-full mt-[15px]">
							<div class="float-right">
								<el-button @click="returnClick">{{ $t('common.cancelButtonText') }}</el-button>
								<el-button v-debounce="confirmClick" type="primary">{{ $t('common.confirmButtonText') }}</el-button>
							</div>
						</div>
					</el-row>
				</div>
			</pane>
		</splitpanes>
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { pageList } from '/@/api/basicData/materialManagement';
import { addObj, putObj, getObj } from '/@/api/basicData/materialCombination';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { useDict } from '/@/hooks/dict';
const { enable_status } = useDict('enable_status');
const MaterialSingle = defineAsyncComponent(() => import('./tags/materialSingle.vue'));
const router = useRouter();
const route = useRoute();

const { t } = useI18n();

//单物资标识
const activeName = ref('singleMaterial');
//右侧列表数据
let rightDataList = ref<any>([]);
// 定义变量内容
const queryRef = ref();

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({});
const { tableStyle } = useTable(state);
const form = reactive({
	matingGroupName: '',
	status: '1',
});
const formRules = ref({
	matingGroupName: [{ required: true, message: '请输入物资组合名称', trigger: 'blur' }],
});
const handleClick = (tab: any, event: Event) => {
	console.log(tab, event);
};

const addToRightList = (row: any, arr?: any[]) => {
  const itemsToAdd = arr || [row];
  const materialCodeMap = new Map(rightDataList.value.map((item:any) => [item.materialCode, item]));

  itemsToAdd.forEach((item: any) => {
    const existingItem:any = materialCodeMap.get(item.materialCode);
    if (existingItem) {
      existingItem.materialNum += item.materialNum;
    } else {
      rightDataList.value.push({ ...item });
    }
  });
};
//右侧删除
const deleteClick = (row: any) => {
	rightDataList.value = rightDataList.value.filter((item: any) => item.materialCode != row.materialCode);
};
//右侧提交
const confirmClick = async () => {
	const valid = await queryRef.value.validate().catch(() => {});
	if (!valid) return false;

	try {
		let data = {
			...form,
			groupDetailParamList: rightDataList.value.map((item: any) => ({
				materialCatalogId: item.materialCatalogId || item.id,
				materialNum: item.materialNum,
			})),
		};
		route.query?.id ? await putObj({ ...data, id: route.query.id }) : await addObj(data);
		useMessage().success(t(route.query?.id ? 'common.editSuccessText' : 'common.addSuccessText'));
		returnClick();
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
	}
};

//获取详情
const getDetails = async () => {
	const { data } = await getObj(route.query?.id as string);
	rightDataList.value = data?.groupDetailListVO || [];
	Object.assign(form, {
		matingGroupName: data?.matingGroupName,
		status: String(data?.status),
	});
};
//返回上级菜单
const returnClick = () => {
	router.replace({
		path: '/basicData/materialCombination/index',
	});
};
onMounted(() => {
	if (route.query?.id) {
		getDetails();
	}
});
</script>
