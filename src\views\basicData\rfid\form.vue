<template>
	<el-dialog :close-on-click-modal="false" :title="form.id ? $t('common.editBtn') : $t('common.addBtn')" draggable v-model="visible" width="600">
		<el-form :model="form" :rules="dataRules" formDialogRef label-width="90px" ref="dataFormRef" v-loading="loading">
			<el-form-item label="所属仓库" prop="warehouseId">
				<el-select :placeholder="t('sysrole.please_select')" v-model="form.warehouseId" multiple >
					<el-option :key="item.id" :label="item.warehouseName" :value="item.id" v-for="item in warehouseList"    ></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="设备类型" prop="deviceType">
				<el-select :placeholder="t('sysrole.please_select')" v-model="form.deviceType">
					<el-option :key="index" :label="item.label" :value="item.value" v-for="(item, index) in deviceTypeList"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="设备名称" prop="deviceName">
				<el-input :placeholder="t('sysrole.input_enter')" v-model="form.deviceName" />
			</el-form-item>

			<el-form-item label="设备IP" prop="ip">
				<el-input :placeholder="t('sysrole.input_enter')" v-model="form.ip" />
			</el-form-item>
			<el-form-item label="端口号" prop="port" v-if="form.deviceType == '2'">
				<el-input :placeholder="t('sysrole.input_enter')" v-model="form.port" />
			</el-form-item>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">{{ $t('common.cancelButtonText') }}</el-button>
				<el-button @click="onSubmit" type="primary" :disabled="loading">{{ $t('common.confirmButtonText') }}</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script lang="ts" name="" setup>
import { useMessage } from '/@/hooks/message';
import { addObj, getObj, putObj, getWarehouse } from '/@/api/basicData/rfid';
import { useI18n } from 'vue-i18n';

const emit = defineEmits(['refresh']);

const { t } = useI18n();

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);

// 提交表单数据
const form = reactive({
	id: '',
	warehouseId: '',
	deviceType: '',
	deviceName: '',
	ip: '',
	port: '',
});

// 定义校验规则
const dataRules = ref({
	warehouseId: [{ required: true, message: '请选择所属仓库', trigger: 'blur' }],
	deviceType: [{ required: true, message: '请选择设备类型', trigger: 'blur' }],
	deviceName: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
	ip: [{ required: true, message: '请输入设备IP', trigger: 'blur' }],
	port: [{ required: true, message: '请输入端口号', trigger: 'blur' }],
});

// 打开弹窗
const openDialog = (id: string) => {
	visible.value = true;
	form.id = '';

	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields();
	});

	if (id) {
		form.id = id;
		getappSocialDetailsData(id);
	}
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;

	try {
		loading.value = true;
		if (form.id) {
			await putObj(form);
			useMessage().success(t('common.editSuccessText'));
		} else {
			await addObj(form);
			useMessage().success(t('common.addSuccessText'));
		}
		visible.value = false; // 关闭弹窗
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};

// 初始化表单数据
const getappSocialDetailsData = (id: string) => {
	// 获取数据
	getObj(id).then((res: any) => {
		Object.assign(form, res.data);
	});
};
let warehouseList = ref<any[]>([]);
const deviceTypeList = ref<any[]>([
	{ label: '通道门', value: '1' },
	{ label: '工作站', value: '2' },
	{ label: '手持机', value: '3' },
	{ label: '打印机', value: '4' },
]);
onMounted(() => {
	getWarehouse().then((res: any) => {
		warehouseList.value = res.data;
	});
});
// 暴露变量
defineExpose({
	openDialog,
});
</script>
