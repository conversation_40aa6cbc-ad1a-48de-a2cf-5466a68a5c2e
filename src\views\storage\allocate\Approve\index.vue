<!-- 场景预案 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full">
					<div class="float-left">
						<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
							<el-form-item label="调拨申请单编号" prop="phone">
								<el-input v-model="state.queryForm.phone" placeholder="请输入" clearable class="!max-w-[180px]" />
							</el-form-item>
							<el-form-item label="接收仓库" prop="logType">
								<el-select placeholder="请选择" clearable v-model="state.queryForm.logType" class="!w-[160px]">
									<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in warnType" />
								</el-select>
							</el-form-item>
							<el-form-item label="调拨状态" prop="logType">
								<el-select placeholder="请选择" clearable v-model="state.queryForm.logType" class="!w-[160px]">
									<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in warnType" />
								</el-select>
							</el-form-item>

							<el-form-item label="申请时间" prop="createTime">
								<el-date-picker
									:end-placeholder="$t('syslog.inputEndPlaceholderTip')"
									:start-placeholder="$t('syslog.inputStartPlaceholderTip')"
									range-separator="To"
									type="datetimerange"
									v-model="state.queryForm.createTime"
									value-format="YYYY-MM-DD HH:mm:ss"
								/>
							</el-form-item>

							<el-form-item>
								<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>
				</div>
			</el-row>

			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				row-key="userId"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="调拨申请单编号" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="调出仓库" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="接收仓库" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="调拨状态" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="申请部门" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="申请人" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="申请时间" prop="name" show-overflow-tooltip></el-table-column>

				<el-table-column :label="$t('common.action')" width="200" fixed="right">
					<template #default="scope">
						<!-- 审批approve-->
						<el-button v-auth="'sys_user_edit'" icon="" text type="primary" @click="confirmClick(scope.row.userId)"> 审批 </el-button>

						<!-- 查看  form-->
						<el-button v-auth="'sys_user_edit'" text type="primary" @click="formClick(scope.row.userId)"> 查看 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"> </pagination>
		</div>
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { delObj, pageList, putObj } from '/@/api/admin/user';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { useDict } from '/@/hooks/dict';

// 定义查询字典
const { warnType } = useDict('warnType');
// 动态引入组件

const { t } = useI18n();

// 定义变量内容
const queryRef = ref();

const router = useRouter();

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		deptId: '',
		username: '',
		phone: '',
	},
	pageList: pageList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);





//查看
const formClick = (id?: any) => {
	router.push({
		path: '/storage/allocate/Approve/form',
		query: { id: id, notCreateTags: 'true' },
	});
};

//审批
const confirmClick = (id?: any) => {
	router.push({
		path: '/storage/allocate/Approve/approve',
		query: { id: id, notCreateTags: 'true' },
	});
};
</script>
