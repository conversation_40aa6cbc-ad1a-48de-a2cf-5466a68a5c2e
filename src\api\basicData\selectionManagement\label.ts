import request from '/@/utils/request';
//仓库下拉
export const getWarehouse = () => {
	return request({
		url: '/admin/warehouse/selectWarehouseList',
		method: 'get',
	});
};
export const pageList = (data?: Object) => {
	return request({
		url: '/admin/elecLabel/getElecLabelPage',
		method: 'post',
		data,
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
};

export function openObj(obj?: Object) {
	return request({
		url: '/admin/elecLabel/locateLight',
		method: 'post',
		data: obj,
		headers: {
			'Content-Type': 'application/json',
		},
	});
}

export const closeObj = (params?: Object) => {
	return request({
		url: '/admin/elecLabel/locateLightOff',
		method: 'get',
		params,
	});
};
