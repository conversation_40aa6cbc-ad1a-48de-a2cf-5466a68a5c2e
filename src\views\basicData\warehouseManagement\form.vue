<template>
	<el-dialog :close-on-click-modal="false" :title="form.id ? $t('common.editBtn') : $t('common.addBtn')" draggable v-model="visible" width="600">
		<el-form :model="form" :rules="dataRules" formDialogRef label-width="90px" ref="dataFormRef" v-loading="loading">
			<el-form-item :label="t('appsocial.warehouseNumbers')" prop="warehouseCode">
				<el-input placeholder="请输入仓库编号" v-model="form.warehouseCode" />
			</el-form-item>

			<el-form-item :label="t('appsocial.warehouseName')" prop="warehouseName">
				<el-input placeholder="请输入仓库名称" v-model="form.warehouseName" />
			</el-form-item>
			<el-form-item :label="t('appsocial.department')" prop="deptId">
				<el-tree-select
					:data="deptData"
					:props="{ value: 'id', label: 'name', children: 'children' }"
					check-strictly
					class="w100"
					clearable
					placeholder="请选择所属部门"
					@change="handleDepartmentChange"
					v-model="form.deptId"
				>
				</el-tree-select>
			</el-form-item>
			<el-form-item :label="t('appsocial.manager')" prop="warehouseAdminIdList">
				<el-select placeholder="请选择管理人员" v-model="form.warehouseAdminIdList" multiple>
					<el-option :key="index" :label="item.name" :value="item.userId" v-for="(item, index) in peopleData"></el-option>
				</el-select>
			</el-form-item>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">{{ $t('common.cancelButtonText') }}</el-button>
				<el-button v-debounce="onSubmit" type="primary" :disabled="loading">{{ $t('common.confirmButtonText') }}</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script lang="ts" name="AppSocialDetailsDialog" setup>
// 定义子组件向父组件传值/事件
import { useDict } from '/@/hooks/dict';
import { useMessage } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { rule } from '/@/utils/validate';
import { deptTree } from '/@/api/admin/dept';
import { getPeople, addObj, putObj, getObj } from '/@/api/basicData/warehouseManagement';

const emit = defineEmits(['refresh']);

const { t } = useI18n();

// 定义变量内容
const dataFormRef = ref();
const deptData = ref<any[]>([]);
const peopleData = ref<any[]>([]);
const visible = ref(false);
const loading = ref(false);


// 提交表单数据
const form = reactive({
	id: '',
	warehouseCode: '',
	warehouseName: '',
	deptId: '',
	warehouseAdminIdList: [],
});

// 定义校验规则
const dataRules = ref({
	warehouseCode: [
		{ required: true, message: '仓库编号不能为空', trigger: 'change' },
		{ validator: rule.twoNumber, errorMsg: '仓库编号只能是两位数字', trigger: 'change' },
	],
	warehouseName: [{ required: true, message: '请输入仓库名称', trigger: 'blur' }],
	deptId: [{ required: true, message: '请选择所属部门', trigger: 'blur' }],
	warehouseAdminIdList: [{ required: true, message: '请选择管理人员', trigger: 'blur' }],
});

// 打开弹窗
const openDialog = (id: string) => {
	visible.value = true;
	form.id = '';
	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields();
	});

	if (id) {
		form.id = id;
		getappSocialDetailsData(id);
	}
	// 加载使用的数据
	getDeptData();
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;
	try {
		loading.value = true;
		if (form.id) {
			await putObj(form);
			useMessage().success(t('common.editSuccessText'));
		} else {
			let { id, ...data } = form;
			await addObj(data);
			useMessage().success(t('common.addSuccessText'));
		}
		visible.value = false; // 关闭弹窗
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};

// 初始化表单数据
const getappSocialDetailsData = (id: string) => {
	// 获取数据
	getObj(id).then((res: any) => {
		Object.assign(form, res.data);
	});
	getPeopleData(form.deptId);
};

// 部门变更时清空管理人员
const handleDepartmentChange = async (id: any) => {
	if (!id) {
		form.deptId = '';
		return;
	} else {
		await getPeopleData(id);
	}
};

// 初始化部门数据
const getDeptData = () => {
	deptTree().then((res) => {
		deptData.value = res.data;
	});
};
// 获取部门下人员
const getPeopleData = async (id: any) => {
	const { records } = (await getPeople({ deptId: id })).data;
	peopleData.value = records;
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
