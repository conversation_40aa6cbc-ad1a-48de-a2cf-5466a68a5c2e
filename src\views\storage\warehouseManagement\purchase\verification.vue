<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row class="">
				<el-col :span="24">
					<Descriptions title="核验入库" :column="5" :label-style="{ fontSize: '16px' }" :content-style="{ fontSize: '16px' }">
						<template #extra> </template>
						<DescriptionsItem label="入库单编号">{{ detailsData?.billCode }}</DescriptionsItem>
						<DescriptionsItem label="仓库">{{ detailsData?.warehouseName }}</DescriptionsItem>
						<DescriptionsItem label="入库单状态">{{ detailsData?.billStatusName }}</DescriptionsItem>
						<DescriptionsItem label="创建人员">{{ detailsData?.createUser }}</DescriptionsItem>
						<DescriptionsItem label="创建时间">{{ detailsData?.createTime }}</DescriptionsItem>
					</Descriptions>
				</el-col>
			</el-row>
			<el-table
				v-loading="state.loading"
				:data="detailsData?.billDetailList"
				height="100%"
				row-key="userId"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="物资编码" prop="materialCode" fixed="left" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资标识" prop="materialIdentify" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资名称" prop="materialName" show-overflow-tooltip width="600"></el-table-column>
				<el-table-column label="数量" prop="num" show-overflow-tooltip></el-table-column>
				<el-table-column label="单位" prop="basicUnit" show-overflow-tooltip></el-table-column>
				<el-table-column label="有效期至" prop="endEffectiveTime" show-overflow-tooltip></el-table-column>
			</el-table>
			<el-row class="mt20">
				<div class="w-full flex justify-between items-center">
					<div>物资明细（当前条码数量：{{ detailsData?.billDetailList.reduce((acc: any, item: any) => acc + item.num, 0) }})</div>
					<div>
						<el-button @click="returnClick">取消</el-button>
						<el-button type="primary" @click="confirmClick">核验无误，入库</el-button>
					</div>
				</div>
			</el-row>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { checkObj, getObj } from '/@/api/storage/warehouseManagement/purchase';

import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
const route = useRoute();

// 动态引入组件

const { t } = useI18n();

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({});
const { tableStyle } = useTable(state);

const router = useRouter();
const returnClick = () => {
	router.replace({ path: '/storage/warehouseManagement/purchase/index' });
};
const confirmClick = async () => {
	try {
		await checkObj(route.query?.id);
		useMessage().success('入库成功');
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};

const detailsData = ref<any>();
onMounted(async () => {
	await getObj(route.query?.id).then((res) => {
		detailsData.value = res.data;
	});
});
</script>

<style lang="scss" scoped>
pre code.hljs {
	width: 65%;
}
</style>
