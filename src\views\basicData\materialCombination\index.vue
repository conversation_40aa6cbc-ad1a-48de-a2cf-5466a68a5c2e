<!-- 物资组合 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full">
					<div class="float-left">
						<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
							<el-form-item label="物资组合名称" prop="phone">
								<el-input v-model="state.queryForm.matingGroupName" placeholder="请输入" clearable style="max-width: 180px" />
							</el-form-item>
							<el-form-item>
								<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>
					<div class="float-right">
						<el-button v-auth="'sys_user_add'" icon="folder-add" type="primary" @click="routerClick()">
							{{ $t('common.addBtn') }}
						</el-button>
					</div>
				</div>
			</el-row>

			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				row-key="id"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="物资组合名称" prop="matingGroupName" show-overflow-tooltip width="600"></el-table-column>
				<el-table-column label="物资数量" prop="materialCatalogNum" show-overflow-tooltip></el-table-column>
				<el-table-column label="启用状态" show-overflow-tooltip>
					<template #default="scope">
						<el-switch v-model="scope.row.status" @change="changeSwitch(scope.row)" :active-value="1" :inactive-value="0"></el-switch>
					</template>
				</el-table-column>
				<el-table-column :label="$t('common.action')" width="200" fixed="right">
					<template #default="scope">
						<el-button
							v-auth="'sys_user_edit'"
							icon="Tickets"
							text
							type="primary"
							@click="userDialogRef.openDialog(scope.row.matingGroupName, scope.row.groupDetailListVO)"
						>
							明细
						</el-button>
						<!-- 修改信息 -->
						<el-button v-auth="'sys_user_edit'" icon="edit-pen" text type="primary" @click="routerClick(scope.row.id)">
							{{ $t('common.editBtn') }}
						</el-button>
						<el-button icon="delete" @click="handleDelete(scope.row.id)" text type="primary" v-auth="'app_social_details_del'"
							>{{ $t('common.delBtn') }}
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"> </pagination>
		</div>

		<user-form ref="userDialogRef" />
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { delObj, pageList, switchRow } from '/@/api/basicData/materialCombination';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';

// 动态引入组件
const UserForm = defineAsyncComponent(() => import('./form.vue'));

const { t } = useI18n();

// 定义变量内容
const userDialogRef = ref();
const queryRef = ref();

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		matingGroupName: '',
	},
	pageList: pageList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, tableStyle } = useTable(state);

//表格内开关 (用户状态)
const changeSwitch = async (row: any) => {
	try {
		await switchRow({ id: row.id, status: row.status });
		getDataList();
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};
// 删除操作
const handleDelete = async (id: string) => {
	try {
		await useMessageBox().confirm(t('common.delConfirmText'));
	} catch {
		return;
	}
	try {
		await delObj(id).catch((err: any) => err);
		getDataList();
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};

//新增 修改页面
const router = useRouter();
const routerClick = (id?: any) => {
	const tagsViewName = id ? `物资组合修改:${id}` : '物资组合新增';
	router.push({
		path: '/basicData/materialCombination/add',
		query: { id: id, tagsViewName: tagsViewName, notCreateTags: 'true' },
	});
};
</script>
