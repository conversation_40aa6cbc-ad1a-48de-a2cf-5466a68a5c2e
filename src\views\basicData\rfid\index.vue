<!-- rfid管理 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full" shadow="hover">
					<div class="float-right mr20">
						<el-button
							@click="formDialogRef.openDialog()"
							class="ml10"
							formDialogRef
							icon="folder-add"
							type="primary"
							v-auth="'app_social_details_add'"
						>
							{{ $t('common.addBtn') }}
						</el-button>
					</div>
				</div>
			</el-row>
			<el-table
				:data="state.dataList"
				class="w-full"
				v-loading="state.loading"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column :label="t('sysrole.serialNumber')" type="index" width="60" />
				<el-table-column label="所属仓库" prop="warehouseName" show-overflow-tooltip />
				<el-table-column label="设备类型" show-overflow-tooltip>
					<template #default="scope">
						{{ getdeviceTypeMap[scope.row.deviceType] || '' }}
					</template>
				</el-table-column>
				<el-table-column label="设备名称" prop="deviceName" show-overflow-tooltip />
				<el-table-column label="设备IP" prop="ip" show-overflow-tooltip />
				<el-table-column :label="$t('common.action')" width="150">
					<template #default="scope">
						<el-button icon="edit-pen" @click="formDialogRef.openDialog(scope.row.id)" text type="primary" v-auth="'app_social_details_edit'"
							>{{ $t('common.editBtn') }}
						</el-button>
						<el-button icon="delete" @click="handleDelete(scope.row.id)" text type="primary" v-auth="'app_social_details_del'"
							>{{ $t('common.delBtn') }}
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination @current-change="currentChangeHandle" @size-change="sizeChangeHandle" v-bind="state.pagination" />
		</div>

		<!-- 编辑、新增  -->
		<form-dialog @refresh="getDataList()" ref="formDialogRef" />
	</div>
</template>

<script lang="ts" name="systemAppSocialDetails" setup>
import { BasicTableProps, useTable } from '/@/hooks/table';
import { delObj, pageList } from '/@/api/basicData/rfid';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';

// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));
const { t } = useI18n();

// 定义变量内容
const formDialogRef = ref();

const state: BasicTableProps = reactive<BasicTableProps>({
	pageList: pageList,
});
const getdeviceTypeMap: any = {
	'1': '通道门',
	'2': '工作站',
	'3': '手持机',
	'4': '打印机',
};
//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, tableStyle } = useTable(state);

// 删除操作
const handleDelete = async (id: string) => {
	try {
		await useMessageBox().confirm(t('common.delConfirmText'));
	} catch {
		return;
	}

	try {
		await delObj(id);
		getDataList();
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};
</script>
