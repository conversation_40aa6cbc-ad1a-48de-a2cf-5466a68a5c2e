<template>
	<div>
		<el-row>
			<div class="mb8 w-full">
				<div class="float-left">
					<el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
						<el-form-item :label="$t('sysrole.warehouse')" prop="warehouseId">
							<el-select v-model="state.queryForm.warehouseId" :placeholder="$t('sysrole.warehouseReminder')" clearable class="!max-w-[180px]">
								<el-option v-for="item in warehouseData" :key="item.id" :label="item.warehouseName" :value="item.id" />
							</el-select>
						</el-form-item>
						<el-form-item label="物资名称" prop="materialName">
							<el-input :placeholder="$t('sysrole.inputRoleNameTip')" v-model="state.queryForm.materialName" clearable class="!max-w-[180px]" />
						</el-form-item>
						<el-form-item>
							<el-button icon="search" type="primary" @click="getDataList">
								{{ $t('common.queryBtn') }}
							</el-button>
						</el-form-item>
					</el-form>
				</div>
				<div class="float-right">
					<el-button icon="folder-add" type="primary" class="ml10" @click="dialogRef.openDialog()" v-auth="'sys_role_add'">
						{{ $t('common.addBtn') }}
					</el-button>
					<el-button plain icon="upload-filled" type="primary" class="ml10" @click="excelUploadRef.show()" v-auth="'sys_user_add'">
						{{ $t('common.importBtn') }}
					</el-button>
				</div>
			</div>
		</el-row>
		<el-table
			:data="state.dataList"
			v-loading="state.loading"
			class="w-full"
			row-key="roleId"
			max-height="620px"
			border
			:cell-style="tableStyle.cellStyle"
			:header-cell-style="tableStyle.headerCellStyle"
		>
			<el-table-column type="index" :label="$t('sysrole.serialNumber')" width="80" />
			<el-table-column prop="warehouseName" :label="$t('sysrole.warehouse')" show-overflow-tooltip></el-table-column>
			<el-table-column prop="materialCode" :label="$t('sysrole.materialCode')" show-overflow-tooltip></el-table-column>
			<el-table-column prop="materialIdentify" :label="$t('sysrole.materialIdentification')" show-overflow-tooltip></el-table-column>
			<el-table-column prop="materialName" label="物资名称" width="300" show-overflow-tooltip></el-table-column>
			<el-table-column prop="threshold" :label="$t('sysrole.inventoryWarningValue')" show-overflow-tooltip></el-table-column>
			<el-table-column :label="$t('common.action')" width="250">
				<template #default="scope">
					<el-button
						text
						type="primary"
						icon="edit-pen"
						v-auth="'sys_role_edit'"
						@click="dialogRef.openDialog(scope.row.id, scope.row.materialName, scope.row.warehouseId, scope.row.threshold)"
						>{{ $t('common.editBtn') }}</el-button
					>
					<el-button text type="primary" icon="delete" v-auth="'sys_role_del'" @click="handleDelete(scope.row.id)"
						>{{ $t('common.delBtn') }}
					</el-button>
				</template>
			</el-table-column>
		</el-table>
		<pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
		<!-- 角色编辑、新增  -->
		<role-dialog ref="dialogRef" @refresh="getDataList()" />
		<!-- 导入角色 -->
		<upload-excel
			ref="excelUploadRef"
			:title="$t('sysrole.importFile')"
			url="/admin/inventoryWarnConf/import"
			temp-url="/admin/inventoryWarnConf/download"
			@refreshDataList="getDataList"
		/>
	</div>
</template>

<script setup lang="ts" name="systemSysMessage">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { pageList, delObj, getWarehouse } from '/@/api/basicData/warningMechanism';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';

// 引入组件
const RoleDialog = defineAsyncComponent(() => import('./form.vue'));
const { t } = useI18n();

// 定义变量内容
const queryRef = ref();
const dialogRef = ref();
const excelUploadRef = ref();
//表单表格数据
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		materialName: '',
		warehouseId: '',
	},
	pageList,
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 删除操作
const handleDelete = async (id: string) => {
	try {
		await useMessageBox().confirm(t('common.delConfirmText'));
	} catch {
		return;
	}

	try {
		await delObj(id);
		getDataList();
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};
//
let warehouseData = ref<any>([]);
onMounted(() => {
	getWarehouse().then((res) => {
		warehouseData.value = res.data;
	});
});
</script>
