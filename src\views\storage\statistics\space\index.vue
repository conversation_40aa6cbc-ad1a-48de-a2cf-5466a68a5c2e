<!-- 场景预案 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full" >
					<div class="float-left">
						<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">

							<el-form-item label="库" prop="logType">
								<el-select placeholder="请选择" clearable v-model="state.queryForm.logType" class="!w-[160px]">
									<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in warnType" />
								</el-select>
							</el-form-item>
							<el-form-item label="区" prop="logType">
								<el-select placeholder="请选择" clearable v-model="state.queryForm.logType" class="!w-[160px]">
									<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in warnType" />
								</el-select>
							</el-form-item>
							<el-form-item label="列号" prop="logType">
								<el-select placeholder="请选择" clearable v-model="state.queryForm.logType" class="!w-[160px]">
									<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in warnType" />
								</el-select>
							</el-form-item>

							<el-form-item>
								<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>
					<!-- <div class="float-right">
						<el-button v-auth="'sys_user_add'" icon="folder-add" type="primary" @click="exportClick">
					导出
						</el-button>
					</div> -->
				</div>
			</el-row>

			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				@selection-change="handleSelectionChange"
				row-key="userId"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="仓库" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="区" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="列号" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="已用货位数" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="总货位数" prop="name" show-overflow-tooltip></el-table-column>
				<el-table-column label="空间利用率" prop="name" show-overflow-tooltip></el-table-column>

			</el-table>
			<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"> </pagination>
		</div>
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { pageList } from '/@/api/admin/user';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { useDict } from '/@/hooks/dict';

// 定义查询字典
const { warnType } = useDict('warnType');
// 动态引入组件

const { t } = useI18n();

// 定义变量内容
const userDialogRef = ref();
const queryRef = ref();

const param_type = ref([]);
// 多选rows
const selectObjs = ref([]) as any;
// 是否可以多选
const multiple = ref(true);

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		deptId: '',
		username: '',
		phone: '',
	},
	pageList: pageList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 多选事件
const handleSelectionChange = (objs: { userId: string }[]) => {
	selectObjs.value = objs.map(({ userId }) => userId);
	multiple.value = !objs.length;
};

const exportClick = async () => {

}
// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm(t('common.delConfirmText'));
	} catch {
		return;
	}

	try {
		await delObj(ids);
		getDataList();
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};


</script>
